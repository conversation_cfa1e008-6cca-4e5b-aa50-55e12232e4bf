# Image Sharpening using Knowledge Distillation

A real-time image sharpening model for video conferencing applications using teacher-student knowledge distillation.

## Project Overview

This project develops an ultra-lightweight AI model that enhances image sharpness during video conferencing, addressing issues like reduced clarity due to low bandwidth or poor internet connections.

### Key Features
- **Real-time Performance**: 30-60 fps or higher
- **High Accuracy**: SSIM score above 90%
- **Lightweight Architecture**: Optimized for resource-constrained environments
- **Knowledge Distillation**: Teacher-student model approach
- **Multiple Deployment Options**: PyTorch, ONNX, and quantized models

## Quick Start

### 1. Installation
```bash
# Clone the repository
git clone <repository-url>
cd image-sharpening-kd

# Install dependencies
pip install -r requirements.txt
```

### 2. Run Complete Example
```bash
# Run the complete workflow (creates sample data, trains, evaluates)
python example.py
```

### 3. Real-time Demo
```bash
# Webcam demo (after training)
python demo.py --model_path results/example/checkpoints/best_model.pth --mode webcam

# Image demo
python demo.py --model_path results/example/checkpoints/best_model.pth --mode image --input your_image.jpg
```

## Project Structure

```
├── data/                   # Dataset directory
│   ├── train/             # Training images
│   ├── val/               # Validation images
│   └── test/              # Test images
├── models/                # Model definitions
│   ├── teacher/           # Teacher model implementations (ESRGAN)
│   ├── student/           # Student model architectures
│   └── pretrained/        # Pre-trained model weights
├── training/              # Training scripts and utilities
│   ├── trainer.py         # Knowledge distillation trainer
│   └── losses.py          # Loss functions
├── evaluation/            # Evaluation and testing scripts
│   └── benchmark.py       # Comprehensive evaluation
├── utils/                 # Utility functions
│   ├── dataset.py         # Dataset loading and preprocessing
│   ├── metrics.py         # Evaluation metrics (SSIM, PSNR, LPIPS)
│   └── optimization.py    # Model optimization utilities
├── configs/               # Configuration files
├── results/               # Training results and outputs
├── train.py              # Main training script
├── evaluate.py           # Evaluation script
├── demo.py               # Real-time demo script
└── example.py            # Complete example workflow
```

## Detailed Usage

### Training

#### 1. Prepare Your Dataset
```bash
# Option 1: Use sample dataset (for testing)
python train.py --create_sample_data --num_samples 1000

# Option 2: Use your own dataset
# Place high-resolution images in data/train/, data/val/, data/test/
```

#### 2. Train the Model
```bash
# Basic training
python train.py --epochs 100 --batch_size 16

# Advanced training with custom settings
python train.py \
    --student_type balanced \
    --epochs 100 \
    --batch_size 16 \
    --lr 1e-4 \
    --experiment_name my_experiment
```

#### 3. Resume Training
```bash
python train.py --resume results/my_experiment/checkpoints/checkpoint_epoch_50.pth
```

### Evaluation

#### 1. Comprehensive Evaluation
```bash
# Evaluate on test dataset
python evaluate.py \
    --model_path results/my_experiment/checkpoints/best_model.pth \
    --test_data_dir data/test \
    --test_performance

# Evaluate specific categories
python evaluate.py \
    --model_path results/my_experiment/checkpoints/best_model.pth \
    --test_data_dir data/test_categories \
    --categories text nature people
```

#### 2. Performance Benchmarking
```bash
# Test FPS on different resolutions
python evaluate.py \
    --model_path results/my_experiment/checkpoints/best_model.pth \
    --test_performance \
    --test_resolution 1920x1080 \
    --num_iterations 100
```

### Real-time Demo

#### 1. Webcam Demo
```bash
# Default webcam
python demo.py --model_path results/my_experiment/checkpoints/best_model.pth --mode webcam

# Specific camera
python demo.py --model_path results/my_experiment/checkpoints/best_model.pth --mode webcam --camera_id 1
```

#### 2. Video Processing
```bash
# Process video file
python demo.py \
    --model_path results/my_experiment/checkpoints/best_model.pth \
    --mode video \
    --input input_video.mp4 \
    --output enhanced_video.mp4
```

#### 3. Image Enhancement
```bash
# Single image
python demo.py \
    --model_path results/my_experiment/checkpoints/best_model.pth \
    --mode image \
    --input input_image.jpg \
    --output enhanced_image.jpg
```

### Model Optimization

The project includes utilities for optimizing models for deployment:

```python
from utils.optimization import optimize_for_deployment

# Optimize trained model
optimized_models = optimize_for_deployment(
    model=your_trained_model,
    calibration_data=val_loader,
    output_dir='optimized_models/',
    quantize=True,      # Apply quantization
    convert_onnx=True,  # Convert to ONNX
    prune=True,         # Apply pruning
    pruning_ratio=0.2   # Remove 20% of parameters
)
```

## Model Architecture

### Teacher Model (ESRGAN)
- High-quality super-resolution model
- 23 RRDB blocks with residual dense connections
- ~16M parameters
- Provides rich feature representations for knowledge transfer

### Student Model Options

#### Ultra-Light Student
- **Parameters**: ~100K
- **Model Size**: ~0.5 MB
- **Target FPS**: 60+
- **Use Case**: Resource-constrained devices

#### Balanced Student
- **Parameters**: ~500K
- **Model Size**: ~2 MB
- **Target FPS**: 30-60
- **Use Case**: Good balance of quality and speed

#### Custom Student
- Configurable architecture
- Adjustable channels, blocks, and features
- Customizable for specific requirements

## Performance Targets

- **Speed**: 30-60 fps on 1920x1080 resolution
- **Quality**: SSIM > 90%
- **Model Size**: < 5MB for deployment
- **Latency**: < 20ms inference time

## Configuration

The project uses a comprehensive configuration system in `configs/config.py`:

```python
# Example configuration updates
from configs.config import config

# Update training parameters
config.training.batch_size = 32
config.training.learning_rate = 2e-4
config.training.num_epochs = 200

# Update model parameters
config.model.student_channels = 64
config.model.student_blocks = 6

# Update evaluation parameters
config.evaluation.target_ssim = 0.92
```

## Requirements

- Python 3.8+
- PyTorch 2.0+
- CUDA-capable GPU (recommended)
- OpenCV (for demo)
- Additional dependencies in requirements.txt
