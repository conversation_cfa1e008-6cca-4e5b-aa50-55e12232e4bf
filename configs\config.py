"""
Configuration file for Image Sharpening Knowledge Distillation
"""

import os
from dataclasses import dataclass
from typing import Tuple, List

@dataclass
class DataConfig:
    """Data configuration"""
    # Dataset paths
    train_dir: str = "data/train"
    val_dir: str = "data/val" 
    test_dir: str = "data/test"
    
    # Image processing
    input_size: Tuple[int, int] = (480, 270)  # Low-res input for training
    target_size: Tuple[int, int] = (1920, 1080)  # Target output resolution
    crop_size: Tuple[int, int] = (256, 256)  # Training crop size for efficiency
    
    # Data augmentation
    use_augmentation: bool = True
    rotation_range: float = 5.0
    brightness_range: float = 0.2
    contrast_range: float = 0.2
    saturation_range: float = 0.2
    
    # Dataset splits
    train_split: float = 0.8
    val_split: float = 0.1
    test_split: float = 0.1

@dataclass
class ModelConfig:
    """Model configuration"""
    # Teacher model
    teacher_model: str = "ESRGAN"  # Options: ESRGAN, Real-ESRGAN, EDSR
    teacher_weights_path: str = "models/pretrained/teacher.pth"
    
    # Student model
    student_channels: int = 32
    student_blocks: int = 4
    use_depthwise_conv: bool = True
    use_attention: bool = False
    
    # Model constraints
    max_parameters: int = 1_000_000  # 1M parameters max
    target_fps: int = 60
    target_model_size_mb: float = 5.0

@dataclass
class TrainingConfig:
    """Training configuration"""
    # Basic training
    batch_size: int = 16
    num_epochs: int = 100
    learning_rate: float = 1e-4
    weight_decay: float = 1e-5
    
    # Knowledge distillation
    distillation_alpha: float = 0.7  # Weight for distillation loss
    temperature: float = 4.0  # Temperature for soft targets
    feature_loss_weight: float = 0.3
    attention_loss_weight: float = 0.2
    
    # Loss functions
    pixel_loss: str = "L1"  # L1, L2, or Huber
    perceptual_loss_weight: float = 0.1
    
    # Optimization
    optimizer: str = "Adam"  # Adam, AdamW, SGD
    scheduler: str = "CosineAnnealingLR"
    warmup_epochs: int = 5
    
    # Checkpointing
    save_every: int = 10
    checkpoint_dir: str = "results/checkpoints"
    
    # Device
    device: str = "cuda" if os.path.exists("/usr/bin/nvidia-smi") else "cpu"
    num_workers: int = 4

@dataclass
class EvaluationConfig:
    """Evaluation configuration"""
    # Metrics
    target_ssim: float = 0.90
    calculate_lpips: bool = True
    calculate_psnr: bool = True
    
    # Benchmark dataset categories
    test_categories: List[str] = None
    min_test_images: int = 100
    
    # Performance testing
    fps_test_resolution: Tuple[int, int] = (1920, 1080)
    fps_test_iterations: int = 100
    
    # Subjective evaluation
    mos_scale: Tuple[int, int] = (1, 5)  # Mean Opinion Score scale
    num_evaluators: int = 10
    
    def __post_init__(self):
        if self.test_categories is None:
            self.test_categories = ["text", "nature", "people", "animals", "games"]

# Global configuration instance
class Config:
    def __init__(self):
        self.data = DataConfig()
        self.model = ModelConfig()
        self.training = TrainingConfig()
        self.evaluation = EvaluationConfig()
        
    def update_from_dict(self, config_dict: dict):
        """Update configuration from dictionary"""
        for section, values in config_dict.items():
            if hasattr(self, section):
                section_config = getattr(self, section)
                for key, value in values.items():
                    if hasattr(section_config, key):
                        setattr(section_config, key, value)

# Default configuration
config = Config()
