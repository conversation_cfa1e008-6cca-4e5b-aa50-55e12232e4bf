#!/usr/bin/env python3
"""
Real-time demo for image sharpening model
"""

import os
import argparse
import cv2
import torch
import numpy as np
from PIL import Image
import time

from models.student.lightweight_sr import create_student_model
from utils.metrics import PerformanceTimer

class RealTimeSharpener:
    """Real-time image sharpening demo"""
    
    def __init__(self, model_path, model_type='balanced', device='auto'):
        self.device = self._setup_device(device)
        self.model = self._load_model(model_path, model_type)
        self.timer = PerformanceTimer()
        
        # Performance tracking
        self.fps_history = []
        self.max_history = 30  # Keep last 30 frames for FPS calculation
        
    def _setup_device(self, device_arg):
        """Setup device"""
        if device_arg == 'auto':
            device = 'cuda' if torch.cuda.is_available() else 'cpu'
        else:
            device = device_arg
        
        if device == 'cuda' and not torch.cuda.is_available():
            print("CUDA not available, falling back to CPU")
            device = 'cpu'
        
        print(f"Using device: {device}")
        return device
    
    def _load_model(self, model_path, model_type):
        """Load trained model"""
        print(f"Loading model from {model_path}...")
        
        checkpoint = torch.load(model_path, map_location=self.device)
        
        # Create model
        if 'model_config' in checkpoint:
            model_config = checkpoint['model_config']
            model = create_student_model(
                model_type=model_config.get('type', model_type),
                scale_factor=model_config.get('scale_factor', 4)
            )
        else:
            model = create_student_model(model_type=model_type, scale_factor=4)
        
        # Load weights
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        elif 'student_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['student_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(self.device)
        model.eval()
        
        print(f"Model loaded: {model.count_parameters():,} parameters, {model.get_model_size_mb():.2f} MB")
        return model
    
    def preprocess_frame(self, frame):
        """Preprocess frame for model input"""
        # Convert BGR to RGB
        frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        
        # Convert to PIL Image
        pil_image = Image.fromarray(frame_rgb)
        
        # Simulate low resolution (downscale then upscale)
        h, w = frame.shape[:2]
        low_res_size = (w // 4, h // 4)  # 4x downscale
        
        # Downscale
        low_res = pil_image.resize(low_res_size, Image.BICUBIC)
        
        # Convert to tensor
        tensor = torch.from_numpy(np.array(low_res)).float() / 255.0
        tensor = tensor.permute(2, 0, 1).unsqueeze(0)  # HWC to BCHW
        
        return tensor.to(self.device)
    
    def postprocess_output(self, output_tensor, target_size):
        """Postprocess model output"""
        # Convert to numpy
        output = output_tensor.squeeze(0).cpu().numpy()
        output = output.transpose(1, 2, 0)  # CHW to HWC
        
        # Clip values
        output = np.clip(output, 0, 1)
        
        # Convert to uint8
        output = (output * 255).astype(np.uint8)
        
        # Resize to target size if needed
        if output.shape[:2] != target_size:
            output = cv2.resize(output, target_size, interpolation=cv2.INTER_LINEAR)
        
        # Convert RGB to BGR for OpenCV
        output_bgr = cv2.cvtColor(output, cv2.COLOR_RGB2BGR)
        
        return output_bgr
    
    def enhance_frame(self, frame):
        """Enhance a single frame"""
        h, w = frame.shape[:2]
        
        # Preprocess
        input_tensor = self.preprocess_frame(frame)
        
        # Inference
        self.timer.start()
        with torch.no_grad():
            with torch.cuda.amp.autocast():
                enhanced_tensor = self.model(input_tensor)
        inference_time = self.timer.end()
        
        # Postprocess
        enhanced_frame = self.postprocess_output(enhanced_tensor, (w, h))
        
        # Update FPS tracking
        fps = 1000.0 / inference_time
        self.fps_history.append(fps)
        if len(self.fps_history) > self.max_history:
            self.fps_history.pop(0)
        
        return enhanced_frame, inference_time
    
    def get_average_fps(self):
        """Get average FPS from recent frames"""
        if not self.fps_history:
            return 0
        return np.mean(self.fps_history)
    
    def demo_webcam(self, camera_id=0, display_size=(1280, 720)):
        """Run real-time demo with webcam"""
        print(f"Starting webcam demo (camera {camera_id})...")
        print("Press 'q' to quit, 's' to save screenshot")
        
        cap = cv2.VideoCapture(camera_id)
        
        if not cap.isOpened():
            raise ValueError(f"Could not open camera {camera_id}")
        
        # Set camera resolution
        cap.set(cv2.CAP_PROP_FRAME_WIDTH, display_size[0])
        cap.set(cv2.CAP_PROP_FRAME_HEIGHT, display_size[1])
        
        screenshot_count = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Enhance frame
            enhanced_frame, inference_time = self.enhance_frame(frame)
            
            # Create comparison view
            comparison = self._create_comparison_view(frame, enhanced_frame)
            
            # Add performance info
            avg_fps = self.get_average_fps()
            self._add_performance_overlay(comparison, inference_time, avg_fps)
            
            # Display
            cv2.imshow('Image Sharpening Demo', comparison)
            
            # Handle key presses
            key = cv2.waitKey(1) & 0xFF
            if key == ord('q'):
                break
            elif key == ord('s'):
                # Save screenshot
                screenshot_path = f'screenshot_{screenshot_count:03d}.png'
                cv2.imwrite(screenshot_path, comparison)
                print(f"Screenshot saved: {screenshot_path}")
                screenshot_count += 1
        
        cap.release()
        cv2.destroyAllWindows()
        
        print(f"Demo ended. Average FPS: {self.get_average_fps():.2f}")
    
    def demo_video(self, video_path, output_path=None):
        """Run demo on video file"""
        print(f"Processing video: {video_path}")
        
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            raise ValueError(f"Could not open video: {video_path}")
        
        # Get video properties
        fps = cap.get(cv2.CAP_PROP_FPS)
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        print(f"Video: {width}x{height}, {fps} FPS, {total_frames} frames")
        
        # Setup output video writer if requested
        writer = None
        if output_path:
            fourcc = cv2.VideoWriter_fourcc(*'mp4v')
            writer = cv2.VideoWriter(output_path, fourcc, fps, (width * 2, height))
        
        frame_count = 0
        total_inference_time = 0
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Enhance frame
            enhanced_frame, inference_time = self.enhance_frame(frame)
            total_inference_time += inference_time
            
            # Create comparison
            comparison = self._create_comparison_view(frame, enhanced_frame)
            
            # Save to output video
            if writer:
                writer.write(comparison)
            
            frame_count += 1
            
            # Print progress
            if frame_count % 30 == 0:
                progress = (frame_count / total_frames) * 100
                avg_fps = self.get_average_fps()
                print(f"Progress: {progress:.1f}% ({frame_count}/{total_frames}), Avg FPS: {avg_fps:.2f}")
        
        cap.release()
        if writer:
            writer.release()
        
        avg_inference_time = total_inference_time / frame_count
        avg_fps = 1000.0 / avg_inference_time
        
        print(f"Video processing completed!")
        print(f"Average inference time: {avg_inference_time:.2f} ms")
        print(f"Average FPS: {avg_fps:.2f}")
        if output_path:
            print(f"Output saved to: {output_path}")
    
    def demo_image(self, image_path, output_path=None):
        """Run demo on single image"""
        print(f"Processing image: {image_path}")
        
        # Load image
        frame = cv2.imread(image_path)
        if frame is None:
            raise ValueError(f"Could not load image: {image_path}")
        
        # Enhance
        enhanced_frame, inference_time = self.enhance_frame(frame)
        
        # Create comparison
        comparison = self._create_comparison_view(frame, enhanced_frame)
        
        # Add performance info
        fps = 1000.0 / inference_time
        self._add_performance_overlay(comparison, inference_time, fps)
        
        # Save or display
        if output_path:
            cv2.imwrite(output_path, comparison)
            print(f"Result saved to: {output_path}")
        else:
            cv2.imshow('Image Sharpening Result', comparison)
            print("Press any key to close...")
            cv2.waitKey(0)
            cv2.destroyAllWindows()
        
        print(f"Inference time: {inference_time:.2f} ms ({fps:.2f} FPS)")
    
    def _create_comparison_view(self, original, enhanced):
        """Create side-by-side comparison"""
        h, w = original.shape[:2]
        
        # Resize if too large for display
        max_width = 640
        if w > max_width:
            scale = max_width / w
            new_w, new_h = int(w * scale), int(h * scale)
            original = cv2.resize(original, (new_w, new_h))
            enhanced = cv2.resize(enhanced, (new_w, new_h))
        
        # Create side-by-side comparison
        comparison = np.hstack([original, enhanced])
        
        # Add labels
        cv2.putText(comparison, 'Original', (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        cv2.putText(comparison, 'Enhanced', (original.shape[1] + 10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
        
        return comparison
    
    def _add_performance_overlay(self, image, inference_time, fps):
        """Add performance information overlay"""
        h, w = image.shape[:2]
        
        # Performance text
        perf_text = f"Inference: {inference_time:.1f}ms | FPS: {fps:.1f}"
        
        # Add background rectangle
        text_size = cv2.getTextSize(perf_text, cv2.FONT_HERSHEY_SIMPLEX, 0.7, 2)[0]
        cv2.rectangle(image, (w - text_size[0] - 20, h - 40), (w, h), (0, 0, 0), -1)
        
        # Add text
        cv2.putText(image, perf_text, (w - text_size[0] - 10, h - 15), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Real-time Image Sharpening Demo')
    
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to trained model checkpoint')
    parser.add_argument('--model_type', type=str, default='balanced',
                       choices=['ultra_light', 'balanced', 'custom'],
                       help='Type of student model')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (cuda/cpu/auto)')
    
    # Demo mode
    parser.add_argument('--mode', type=str, default='webcam',
                       choices=['webcam', 'video', 'image'],
                       help='Demo mode')
    
    # Input/output
    parser.add_argument('--input', type=str, default=None,
                       help='Input file path (for video/image mode)')
    parser.add_argument('--output', type=str, default=None,
                       help='Output file path')
    parser.add_argument('--camera_id', type=int, default=0,
                       help='Camera ID for webcam mode')
    
    return parser.parse_args()

def main():
    """Main demo function"""
    args = parse_args()
    
    print("="*50)
    print("Real-time Image Sharpening Demo")
    print("="*50)
    
    # Create sharpener
    sharpener = RealTimeSharpener(
        model_path=args.model_path,
        model_type=args.model_type,
        device=args.device
    )
    
    # Run demo based on mode
    try:
        if args.mode == 'webcam':
            sharpener.demo_webcam(camera_id=args.camera_id)
        
        elif args.mode == 'video':
            if not args.input:
                raise ValueError("Input video path required for video mode")
            sharpener.demo_video(args.input, args.output)
        
        elif args.mode == 'image':
            if not args.input:
                raise ValueError("Input image path required for image mode")
            sharpener.demo_image(args.input, args.output)
        
    except KeyboardInterrupt:
        print("\nDemo interrupted by user")
    except Exception as e:
        print(f"Demo failed: {e}")
        raise

if __name__ == '__main__':
    main()
