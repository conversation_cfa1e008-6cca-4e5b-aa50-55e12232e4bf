#!/usr/bin/env python3
"""
Evaluation script for trained image sharpening models
"""

import os
import argparse
import torch
import json

from models.student.lightweight_sr import create_student_model
from evaluation.benchmark import run_comprehensive_evaluation
from configs.config import config

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Evaluate Image Sharpening Model')
    
    # Model arguments
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to trained model checkpoint')
    parser.add_argument('--model_type', type=str, default='balanced',
                       choices=['ultra_light', 'balanced', 'custom'],
                       help='Type of student model')
    
    # Data arguments
    parser.add_argument('--test_data_dir', type=str, default='data/test',
                       help='Directory containing test data')
    parser.add_argument('--categories', nargs='+', default=None,
                       help='Specific categories to evaluate')
    
    # Evaluation arguments
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (cuda/cpu/auto)')
    parser.add_argument('--batch_size', type=int, default=1,
                       help='Batch size for evaluation')
    parser.add_argument('--save_images', action='store_true',
                       help='Save comparison images')
    
    # Performance testing
    parser.add_argument('--test_performance', action='store_true',
                       help='Run performance benchmarks')
    parser.add_argument('--test_resolution', type=str, default='1920x1080',
                       help='Resolution for performance testing (WxH)')
    parser.add_argument('--num_iterations', type=int, default=100,
                       help='Number of iterations for performance testing')
    
    # Output arguments
    parser.add_argument('--output_dir', type=str, default='results/evaluation',
                       help='Directory to save evaluation results')
    parser.add_argument('--experiment_name', type=str, default='evaluation',
                       help='Name of the evaluation experiment')
    
    return parser.parse_args()

def setup_device(device_arg):
    """Setup device for evaluation"""
    if device_arg == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = device_arg
    
    if device == 'cuda' and not torch.cuda.is_available():
        print("CUDA not available, falling back to CPU")
        device = 'cpu'
    
    print(f"Using device: {device}")
    return device

def load_model(model_path, model_type, device):
    """Load trained model from checkpoint"""
    print(f"Loading model from {model_path}...")
    
    # Load checkpoint
    checkpoint = torch.load(model_path, map_location=device)
    
    # Create model
    if 'model_config' in checkpoint:
        # Use config from checkpoint if available
        model_config = checkpoint['model_config']
        model = create_student_model(
            model_type=model_config.get('type', model_type),
            scale_factor=model_config.get('scale_factor', 4)
        )
    else:
        # Use provided model type
        model = create_student_model(model_type=model_type, scale_factor=4)
    
    # Load state dict
    if 'model_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['model_state_dict'])
    elif 'student_state_dict' in checkpoint:
        model.load_state_dict(checkpoint['student_state_dict'])
    else:
        model.load_state_dict(checkpoint)
    
    model = model.to(device)
    model.eval()
    
    print(f"Model loaded successfully")
    print(f"  Parameters: {model.count_parameters():,}")
    print(f"  Model size: {model.get_model_size_mb():.2f} MB")
    
    return model

def parse_resolution(resolution_str):
    """Parse resolution string (e.g., '1920x1080') to tuple"""
    try:
        width, height = resolution_str.split('x')
        return (int(width), int(height))
    except:
        raise ValueError(f"Invalid resolution format: {resolution_str}. Use WxH format (e.g., 1920x1080)")

def evaluate_model(model, args, device):
    """Run model evaluation"""
    print("Starting model evaluation...")
    
    # Setup output directory
    output_dir = os.path.join(args.output_dir, args.experiment_name)
    os.makedirs(output_dir, exist_ok=True)
    
    results = {}
    
    # 1. Comprehensive evaluation on test dataset
    if os.path.exists(args.test_data_dir):
        print(f"Evaluating on test dataset: {args.test_data_dir}")
        
        # Update config for evaluation
        if args.categories:
            config.evaluation.test_categories = args.categories
        
        # Run comprehensive evaluation
        comprehensive_results = run_comprehensive_evaluation(
            model=model,
            test_data_dir=args.test_data_dir,
            device=device
        )
        
        results['comprehensive'] = comprehensive_results
        
        # Save comprehensive results
        with open(os.path.join(output_dir, 'comprehensive_results.json'), 'w') as f:
            json.dump(comprehensive_results, f, indent=2)
        
    else:
        print(f"Warning: Test data directory not found: {args.test_data_dir}")
    
    # 2. Performance testing
    if args.test_performance:
        print("Running performance benchmarks...")
        
        from evaluation.benchmark import BenchmarkEvaluator
        evaluator = BenchmarkEvaluator(model, device, save_results=False)
        
        # Test on specified resolution
        test_resolution = parse_resolution(args.test_resolution)
        performance_results = evaluator.evaluate_performance(
            input_size=test_resolution,
            num_iterations=args.num_iterations
        )
        
        results['performance'] = performance_results
        
        # Save performance results
        with open(os.path.join(output_dir, 'performance_results.json'), 'w') as f:
            json.dump(performance_results, f, indent=2)
        
        print(f"Performance Results:")
        print(f"  Average FPS: {performance_results['fps']:.2f}")
        print(f"  Average inference time: {performance_results['mean_time_ms']:.2f} ms")
        print(f"  Test resolution: {test_resolution}")
    
    return results

def print_summary(results):
    """Print evaluation summary"""
    print("\n" + "="*60)
    print("EVALUATION SUMMARY")
    print("="*60)
    
    if 'comprehensive' in results:
        comp_results = results['comprehensive']
        
        if 'categories' in comp_results and 'overall' in comp_results['categories']:
            overall = comp_results['categories']['overall']
            print(f"Overall SSIM: {overall['overall_ssim_mean']:.4f} ± {overall['overall_ssim_std']:.4f}")
            print(f"Target SSIM (≥{config.evaluation.target_ssim}) achieved: {overall['target_ssim_achieved']}")
            print(f"Overall PSNR: {overall['overall_psnr_mean']:.2f} ± {overall['overall_psnr_std']:.2f}")
            print(f"Number of categories: {overall['num_categories']}")
        
        if 'performance' in comp_results:
            perf = comp_results['performance']
            print(f"Average FPS: {perf['fps']:.2f}")
            print(f"Model size: {comp_results['model_info']['model_size_mb']} MB")
            print(f"Parameters: {comp_results['model_info']['parameters']}")
    
    if 'performance' in results:
        perf = results['performance']
        print(f"\nPerformance Test Results:")
        print(f"  FPS: {perf['fps']:.2f}")
        print(f"  Inference time: {perf['mean_time_ms']:.2f} ± {perf['std_time_ms']:.2f} ms")
        print(f"  Test resolution: {perf['input_resolution']}")
    
    print("="*60)

def main():
    """Main evaluation function"""
    args = parse_args()
    
    print("="*50)
    print("Image Sharpening Model Evaluation")
    print("="*50)
    
    # Setup device
    device = setup_device(args.device)
    
    # Load model
    model = load_model(args.model_path, args.model_type, device)
    
    # Run evaluation
    results = evaluate_model(model, args, device)
    
    # Print summary
    print_summary(results)
    
    print(f"\nEvaluation completed!")
    print(f"Results saved to: {os.path.join(args.output_dir, args.experiment_name)}")

if __name__ == '__main__':
    main()
