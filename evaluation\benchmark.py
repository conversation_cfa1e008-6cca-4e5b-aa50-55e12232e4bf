"""
Benchmark evaluation for image sharpening models
"""

import os
import json
import time
import torch
import numpy as np
from PIL import Image
from typing import Dict, List, Tuple, Optional
from tqdm import tqdm
import matplotlib.pyplot as plt

from utils.metrics import ImageMetrics, PerformanceTimer
from utils.dataset import VideoConferencingDataset
from configs.config import config

class BenchmarkEvaluator:
    """Comprehensive benchmark evaluation"""
    
    def __init__(
        self,
        model: torch.nn.Module,
        device: str = 'cuda',
        save_results: bool = True,
        results_dir: str = 'results/evaluation'
    ):
        self.model = model.to(device)
        self.device = device
        self.save_results = save_results
        self.results_dir = results_dir
        
        # Initialize metrics
        self.metrics = ImageMetrics(device)
        self.timer = PerformanceTimer()
        
        # Create results directory
        if save_results:
            os.makedirs(results_dir, exist_ok=True)
        
        # Set model to evaluation mode
        self.model.eval()
    
    def evaluate_dataset(
        self,
        test_loader: torch.utils.data.DataLoader,
        category: str = "general"
    ) -> Dict[str, float]:
        """
        Evaluate model on a dataset
        
        Args:
            test_loader: DataLoader for test dataset
            category: Category name for the dataset
            
        Returns:
            Dictionary of evaluation metrics
        """
        print(f"Evaluating on {category} dataset...")
        
        results = {
            'ssim_scores': [],
            'psnr_scores': [],
            'lpips_scores': [],
            'inference_times': [],
            'category': category
        }
        
        with torch.no_grad():
            for batch_idx, batch in enumerate(tqdm(test_loader, desc=f"Evaluating {category}")):
                low_res = batch['low_res'].to(self.device)
                high_res = batch['high_res'].to(self.device)
                bicubic_upscaled = batch['bicubic_upscaled'].to(self.device)
                
                # Measure inference time
                self.timer.start()
                
                # Forward pass
                with torch.cuda.amp.autocast():
                    enhanced = self.model(low_res)
                
                inference_time = self.timer.end()
                results['inference_times'].append(inference_time)
                
                # Calculate metrics for each image in batch
                for i in range(enhanced.shape[0]):
                    enhanced_img = enhanced[i].cpu().numpy().transpose(1, 2, 0)
                    gt_img = high_res[i].cpu().numpy().transpose(1, 2, 0)
                    bicubic_img = bicubic_upscaled[i].cpu().numpy().transpose(1, 2, 0)
                    
                    # Clip values to [0, 1]
                    enhanced_img = np.clip(enhanced_img, 0, 1)
                    gt_img = np.clip(gt_img, 0, 1)
                    bicubic_img = np.clip(bicubic_img, 0, 1)
                    
                    # Calculate SSIM
                    ssim_enhanced = self.metrics.calculate_ssim(enhanced_img, gt_img)
                    ssim_bicubic = self.metrics.calculate_ssim(bicubic_img, gt_img)
                    
                    # Calculate PSNR
                    psnr_enhanced = self.metrics.calculate_psnr(enhanced_img, gt_img)
                    psnr_bicubic = self.metrics.calculate_psnr(bicubic_img, gt_img)
                    
                    # Calculate LPIPS (perceptual similarity)
                    enhanced_tensor = enhanced[i:i+1] * 2 - 1  # Convert to [-1, 1]
                    gt_tensor = high_res[i:i+1] * 2 - 1
                    lpips_score = self.metrics.calculate_lpips(enhanced_tensor, gt_tensor)
                    
                    results['ssim_scores'].append({
                        'enhanced': ssim_enhanced,
                        'bicubic': ssim_bicubic,
                        'improvement': ssim_enhanced - ssim_bicubic
                    })
                    
                    results['psnr_scores'].append({
                        'enhanced': psnr_enhanced,
                        'bicubic': psnr_bicubic,
                        'improvement': psnr_enhanced - psnr_bicubic
                    })
                    
                    results['lpips_scores'].append(lpips_score)
                    
                    # Save sample images for visual inspection
                    if self.save_results and batch_idx < 5 and i == 0:
                        self._save_comparison_image(
                            low_res[i], bicubic_img, enhanced_img, gt_img,
                            f"{category}_sample_{batch_idx}.png"
                        )
        
        # Calculate summary statistics
        summary = self._calculate_summary_stats(results)
        
        # Save detailed results
        if self.save_results:
            self._save_results(results, summary, category)
        
        return summary
    
    def evaluate_performance(
        self,
        input_size: Tuple[int, int] = (1920, 1080),
        num_iterations: int = 100
    ) -> Dict[str, float]:
        """
        Evaluate inference performance (FPS)
        
        Args:
            input_size: Input image size for testing
            num_iterations: Number of iterations for timing
            
        Returns:
            Performance metrics
        """
        print(f"Evaluating performance on {input_size} resolution...")
        
        # Create dummy input
        dummy_input = torch.randn(1, 3, input_size[1], input_size[0]).to(self.device)
        
        # Warmup
        with torch.no_grad():
            for _ in range(10):
                _ = self.model(dummy_input)
        
        # Measure performance
        times = []
        with torch.no_grad():
            for _ in tqdm(range(num_iterations), desc="Performance test"):
                self.timer.start()
                
                with torch.cuda.amp.autocast():
                    _ = self.model(dummy_input)
                
                elapsed = self.timer.end()
                times.append(elapsed)
        
        # Calculate statistics
        times = np.array(times)
        fps = 1000.0 / np.mean(times)  # Convert ms to FPS
        
        performance_stats = {
            'mean_time_ms': np.mean(times),
            'std_time_ms': np.std(times),
            'min_time_ms': np.min(times),
            'max_time_ms': np.max(times),
            'fps': fps,
            'input_resolution': input_size,
            'num_iterations': num_iterations
        }
        
        print(f"Average FPS: {fps:.2f}")
        print(f"Average inference time: {np.mean(times):.2f} ms")
        
        return performance_stats
    
    def evaluate_categories(
        self,
        test_data_dir: str,
        categories: List[str] = None
    ) -> Dict[str, Dict[str, float]]:
        """
        Evaluate on different image categories
        
        Args:
            test_data_dir: Directory containing test images organized by category
            categories: List of categories to evaluate
            
        Returns:
            Results for each category
        """
        if categories is None:
            categories = config.evaluation.test_categories
        
        all_results = {}
        
        for category in categories:
            category_dir = os.path.join(test_data_dir, category)
            
            if not os.path.exists(category_dir):
                print(f"Warning: Category directory {category_dir} not found, skipping...")
                continue
            
            # Create dataset for this category
            dataset = VideoConferencingDataset(
                category_dir,
                input_size=config.data.input_size,
                target_size=config.data.target_size,
                crop_size=None,  # Use full resolution for testing
                augment=False
            )
            
            if len(dataset) == 0:
                print(f"Warning: No images found in {category_dir}, skipping...")
                continue
            
            # Create dataloader
            dataloader = torch.utils.data.DataLoader(
                dataset, batch_size=1, shuffle=False, num_workers=2
            )
            
            # Evaluate
            category_results = self.evaluate_dataset(dataloader, category)
            all_results[category] = category_results
        
        # Calculate overall statistics
        overall_results = self._calculate_overall_stats(all_results)
        all_results['overall'] = overall_results
        
        return all_results
    
    def _calculate_summary_stats(self, results: Dict) -> Dict[str, float]:
        """Calculate summary statistics from detailed results"""
        ssim_enhanced = [score['enhanced'] for score in results['ssim_scores']]
        ssim_bicubic = [score['bicubic'] for score in results['ssim_scores']]
        ssim_improvements = [score['improvement'] for score in results['ssim_scores']]
        
        psnr_enhanced = [score['enhanced'] for score in results['psnr_scores']]
        psnr_bicubic = [score['bicubic'] for score in results['psnr_scores']]
        psnr_improvements = [score['improvement'] for score in results['psnr_scores']]
        
        lpips_scores = results['lpips_scores']
        inference_times = results['inference_times']
        
        summary = {
            'ssim_mean': np.mean(ssim_enhanced),
            'ssim_std': np.std(ssim_enhanced),
            'ssim_min': np.min(ssim_enhanced),
            'ssim_max': np.max(ssim_enhanced),
            'ssim_bicubic_mean': np.mean(ssim_bicubic),
            'ssim_improvement_mean': np.mean(ssim_improvements),
            'ssim_improvement_std': np.std(ssim_improvements),
            
            'psnr_mean': np.mean(psnr_enhanced),
            'psnr_std': np.std(psnr_enhanced),
            'psnr_bicubic_mean': np.mean(psnr_bicubic),
            'psnr_improvement_mean': np.mean(psnr_improvements),
            
            'lpips_mean': np.mean(lpips_scores),
            'lpips_std': np.std(lpips_scores),
            
            'fps': 1000.0 / np.mean(inference_times),
            'inference_time_mean_ms': np.mean(inference_times),
            'inference_time_std_ms': np.std(inference_times),
            
            'num_images': len(ssim_enhanced),
            'target_ssim_achieved': np.mean(ssim_enhanced) >= config.evaluation.target_ssim
        }
        
        return summary
    
    def _calculate_overall_stats(self, category_results: Dict) -> Dict[str, float]:
        """Calculate overall statistics across all categories"""
        all_ssim = []
        all_psnr = []
        all_lpips = []
        all_fps = []
        
        for category, results in category_results.items():
            if category != 'overall':
                all_ssim.append(results['ssim_mean'])
                all_psnr.append(results['psnr_mean'])
                all_lpips.append(results['lpips_mean'])
                all_fps.append(results['fps'])
        
        return {
            'overall_ssim_mean': np.mean(all_ssim),
            'overall_ssim_std': np.std(all_ssim),
            'overall_psnr_mean': np.mean(all_psnr),
            'overall_psnr_std': np.std(all_psnr),
            'overall_lpips_mean': np.mean(all_lpips),
            'overall_fps_mean': np.mean(all_fps),
            'target_ssim_achieved': np.mean(all_ssim) >= config.evaluation.target_ssim,
            'num_categories': len(all_ssim)
        }
    
    def _save_comparison_image(
        self,
        low_res: torch.Tensor,
        bicubic: np.ndarray,
        enhanced: np.ndarray,
        ground_truth: np.ndarray,
        filename: str
    ):
        """Save comparison image showing all versions"""
        fig, axes = plt.subplots(1, 4, figsize=(16, 4))
        
        # Low resolution (upsampled for display)
        low_res_np = low_res.cpu().numpy().transpose(1, 2, 0)
        low_res_display = np.clip(low_res_np, 0, 1)
        axes[0].imshow(low_res_display)
        axes[0].set_title('Low Resolution Input')
        axes[0].axis('off')
        
        # Bicubic upsampled
        axes[1].imshow(bicubic)
        axes[1].set_title('Bicubic Upsampled')
        axes[1].axis('off')
        
        # Enhanced (our model)
        axes[2].imshow(enhanced)
        axes[2].set_title('Enhanced (Our Model)')
        axes[2].axis('off')
        
        # Ground truth
        axes[3].imshow(ground_truth)
        axes[3].set_title('Ground Truth')
        axes[3].axis('off')
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.results_dir, filename), dpi=150, bbox_inches='tight')
        plt.close()
    
    def _save_results(self, detailed_results: Dict, summary: Dict, category: str):
        """Save evaluation results to files"""
        # Save summary as JSON
        summary_path = os.path.join(self.results_dir, f'{category}_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)
        
        # Save detailed results
        detailed_path = os.path.join(self.results_dir, f'{category}_detailed.json')
        
        # Convert numpy arrays to lists for JSON serialization
        serializable_results = {}
        for key, value in detailed_results.items():
            if key in ['ssim_scores', 'psnr_scores']:
                serializable_results[key] = value
            elif key in ['lpips_scores', 'inference_times']:
                serializable_results[key] = [float(x) for x in value]
            else:
                serializable_results[key] = value
        
        with open(detailed_path, 'w') as f:
            json.dump(serializable_results, f, indent=2)
        
        print(f"Results saved to {self.results_dir}")

def run_comprehensive_evaluation(
    model: torch.nn.Module,
    test_data_dir: str,
    device: str = 'cuda'
) -> Dict[str, Dict[str, float]]:
    """
    Run comprehensive evaluation including all metrics and performance tests
    
    Args:
        model: Trained model to evaluate
        test_data_dir: Directory containing test data
        device: Device to run evaluation on
        
    Returns:
        Complete evaluation results
    """
    evaluator = BenchmarkEvaluator(model, device)
    
    print("Starting comprehensive evaluation...")
    
    # 1. Evaluate on different categories
    category_results = evaluator.evaluate_categories(test_data_dir)
    
    # 2. Performance evaluation
    performance_results = evaluator.evaluate_performance(
        input_size=config.evaluation.fps_test_resolution,
        num_iterations=config.evaluation.fps_test_iterations
    )
    
    # Combine results
    final_results = {
        'categories': category_results,
        'performance': performance_results,
        'model_info': {
            'parameters': model.count_parameters() if hasattr(model, 'count_parameters') else 'unknown',
            'model_size_mb': model.get_model_size_mb() if hasattr(model, 'get_model_size_mb') else 'unknown'
        }
    }
    
    # Print summary
    print("\n" + "="*50)
    print("EVALUATION SUMMARY")
    print("="*50)
    
    if 'overall' in category_results:
        overall = category_results['overall']
        print(f"Overall SSIM: {overall['overall_ssim_mean']:.4f} ± {overall['overall_ssim_std']:.4f}")
        print(f"Target SSIM (≥{config.evaluation.target_ssim}) achieved: {overall['target_ssim_achieved']}")
        print(f"Overall PSNR: {overall['overall_psnr_mean']:.2f} ± {overall['overall_psnr_std']:.2f}")
    
    print(f"Average FPS: {performance_results['fps']:.2f}")
    print(f"Model size: {final_results['model_info']['model_size_mb']} MB")
    print(f"Parameters: {final_results['model_info']['parameters']}")
    
    return final_results
