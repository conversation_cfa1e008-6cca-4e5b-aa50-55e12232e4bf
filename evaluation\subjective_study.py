"""
Subjective evaluation study for Mean Opinion Score (MOS)
"""

import os
import json
import random
import tkinter as tk
from tkinter import ttk, messagebox
from PIL import Image, ImageTk
import numpy as np
from typing import Dict, List, Tuple, Optional

class SubjectiveEvaluationGUI:
    """GUI for conducting subjective evaluation studies"""
    
    def __init__(self, image_pairs: List[Tuple[str, str, str]], output_file: str):
        """
        Args:
            image_pairs: List of (original_path, enhanced_path, category) tuples
            output_file: Path to save evaluation results
        """
        self.image_pairs = image_pairs
        self.output_file = output_file
        self.current_index = 0
        self.scores = {}
        self.evaluator_info = {}
        
        # Shuffle image pairs for random evaluation order
        random.shuffle(self.image_pairs)
        
        # Create GUI
        self.root = tk.Tk()
        self.root.title("Image Sharpening Quality Evaluation")
        self.root.geometry("1400x800")
        
        self.setup_gui()
        self.load_current_image()
    
    def setup_gui(self):
        """Setup the GUI components"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Image Sharpening Quality Evaluation", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Progress info
        self.progress_label = ttk.Label(main_frame, text="", font=("Arial", 12))
        self.progress_label.grid(row=0, column=3, sticky=tk.E, pady=(0, 20))
        
        # Image display frame
        image_frame = ttk.Frame(main_frame)
        image_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        image_frame.columnconfigure(0, weight=1)
        image_frame.columnconfigure(1, weight=1)
        
        # Original image
        original_frame = ttk.LabelFrame(image_frame, text="Original Image", padding="10")
        original_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        self.original_label = ttk.Label(original_frame)
        self.original_label.pack()
        
        # Enhanced image
        enhanced_frame = ttk.LabelFrame(image_frame, text="Enhanced Image", padding="10")
        enhanced_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(10, 0))
        self.enhanced_label = ttk.Label(enhanced_frame)
        self.enhanced_label.pack()
        
        # Evaluation frame
        eval_frame = ttk.LabelFrame(main_frame, text="Quality Evaluation", padding="10")
        eval_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Instructions
        instructions = ("Please rate the quality of the enhanced image compared to the original.\n"
                       "Consider sharpness, clarity, artifacts, and overall visual quality.\n"
                       "Scale: 1 (Much Worse) - 2 (Worse) - 3 (Same) - 4 (Better) - 5 (Much Better)")
        
        ttk.Label(eval_frame, text=instructions, font=("Arial", 10)).grid(row=0, column=0, columnspan=6, pady=(0, 15))
        
        # Rating scale
        self.rating_var = tk.IntVar(value=3)
        
        rating_frame = ttk.Frame(eval_frame)
        rating_frame.grid(row=1, column=0, columnspan=6)
        
        ratings = [
            (1, "Much Worse"),
            (2, "Worse"),
            (3, "Same"),
            (4, "Better"),
            (5, "Much Better")
        ]
        
        for i, (value, text) in enumerate(ratings):
            ttk.Radiobutton(rating_frame, text=f"{value}: {text}", variable=self.rating_var, 
                           value=value).grid(row=0, column=i, padx=10, pady=5)
        
        # Comments
        ttk.Label(eval_frame, text="Comments (optional):").grid(row=2, column=0, sticky=tk.W, pady=(15, 5))
        self.comment_text = tk.Text(eval_frame, height=3, width=80)
        self.comment_text.grid(row=3, column=0, columnspan=6, sticky=(tk.W, tk.E), pady=(0, 15))
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=4, sticky=(tk.W, tk.E))
        
        ttk.Button(button_frame, text="Previous", command=self.previous_image).grid(row=0, column=0, padx=(0, 10))
        ttk.Button(button_frame, text="Next", command=self.next_image).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="Save & Exit", command=self.save_and_exit).grid(row=0, column=2, padx=(20, 0))
        
        # Category info
        self.category_label = ttk.Label(main_frame, text="", font=("Arial", 10, "italic"))
        self.category_label.grid(row=4, column=0, columnspan=4, pady=(10, 0))
    
    def load_current_image(self):
        """Load and display current image pair"""
        if self.current_index >= len(self.image_pairs):
            self.finish_evaluation()
            return
        
        original_path, enhanced_path, category = self.image_pairs[self.current_index]
        
        # Update progress
        progress_text = f"Image {self.current_index + 1} of {len(self.image_pairs)}"
        self.progress_label.config(text=progress_text)
        
        # Update category
        self.category_label.config(text=f"Category: {category}")
        
        # Load images
        try:
            # Load and resize images for display
            max_size = (500, 400)
            
            original_img = Image.open(original_path)
            original_img.thumbnail(max_size, Image.Resampling.LANCZOS)
            original_photo = ImageTk.PhotoImage(original_img)
            self.original_label.config(image=original_photo)
            self.original_label.image = original_photo  # Keep reference
            
            enhanced_img = Image.open(enhanced_path)
            enhanced_img.thumbnail(max_size, Image.Resampling.LANCZOS)
            enhanced_photo = ImageTk.PhotoImage(enhanced_img)
            self.enhanced_label.config(image=enhanced_photo)
            self.enhanced_label.image = enhanced_photo  # Keep reference
            
        except Exception as e:
            messagebox.showerror("Error", f"Could not load images: {e}")
        
        # Load previous rating if exists
        image_id = f"image_{self.current_index}"
        if image_id in self.scores:
            self.rating_var.set(self.scores[image_id]['rating'])
            self.comment_text.delete(1.0, tk.END)
            self.comment_text.insert(1.0, self.scores[image_id]['comment'])
        else:
            self.rating_var.set(3)
            self.comment_text.delete(1.0, tk.END)
    
    def save_current_rating(self):
        """Save current rating"""
        image_id = f"image_{self.current_index}"
        original_path, enhanced_path, category = self.image_pairs[self.current_index]
        
        self.scores[image_id] = {
            'original_path': original_path,
            'enhanced_path': enhanced_path,
            'category': category,
            'rating': self.rating_var.get(),
            'comment': self.comment_text.get(1.0, tk.END).strip()
        }
    
    def next_image(self):
        """Go to next image"""
        self.save_current_rating()
        self.current_index += 1
        self.load_current_image()
    
    def previous_image(self):
        """Go to previous image"""
        if self.current_index > 0:
            self.save_current_rating()
            self.current_index -= 1
            self.load_current_image()
    
    def save_and_exit(self):
        """Save results and exit"""
        self.save_current_rating()
        self.save_results()
        self.root.quit()
    
    def finish_evaluation(self):
        """Finish evaluation when all images are rated"""
        messagebox.showinfo("Evaluation Complete", 
                           "Thank you! You have completed the evaluation of all images.")
        self.save_results()
        self.root.quit()
    
    def save_results(self):
        """Save evaluation results to file"""
        # Calculate statistics
        ratings = [score['rating'] for score in self.scores.values()]
        
        results = {
            'evaluator_info': self.evaluator_info,
            'evaluation_date': str(np.datetime64('now')),
            'total_images': len(self.image_pairs),
            'completed_images': len(self.scores),
            'scores': self.scores,
            'statistics': {
                'mean_score': np.mean(ratings) if ratings else 0,
                'std_score': np.std(ratings) if ratings else 0,
                'min_score': np.min(ratings) if ratings else 0,
                'max_score': np.max(ratings) if ratings else 0,
                'score_distribution': {i: ratings.count(i) for i in range(1, 6)}
            }
        }
        
        # Save to file
        os.makedirs(os.path.dirname(self.output_file), exist_ok=True)
        with open(self.output_file, 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"Results saved to: {self.output_file}")
    
    def run(self):
        """Run the evaluation GUI"""
        # Get evaluator information
        self.get_evaluator_info()
        
        # Start GUI
        self.root.mainloop()
    
    def get_evaluator_info(self):
        """Get evaluator information"""
        info_window = tk.Toplevel(self.root)
        info_window.title("Evaluator Information")
        info_window.geometry("400x300")
        info_window.transient(self.root)
        info_window.grab_set()
        
        # Center the window
        info_window.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))
        
        frame = ttk.Frame(info_window, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Fields
        ttk.Label(frame, text="Please provide your information:", font=("Arial", 12, "bold")).pack(pady=(0, 20))
        
        # Name
        ttk.Label(frame, text="Name (optional):").pack(anchor=tk.W)
        name_entry = ttk.Entry(frame, width=30)
        name_entry.pack(pady=(0, 10), fill=tk.X)
        
        # Age
        ttk.Label(frame, text="Age (optional):").pack(anchor=tk.W)
        age_entry = ttk.Entry(frame, width=30)
        age_entry.pack(pady=(0, 10), fill=tk.X)
        
        # Experience
        ttk.Label(frame, text="Experience with image quality evaluation:").pack(anchor=tk.W)
        experience_var = tk.StringVar(value="Beginner")
        experience_combo = ttk.Combobox(frame, textvariable=experience_var, 
                                       values=["Beginner", "Intermediate", "Expert"], 
                                       state="readonly", width=27)
        experience_combo.pack(pady=(0, 10), fill=tk.X)
        
        # Display quality
        ttk.Label(frame, text="Display quality:").pack(anchor=tk.W)
        display_var = tk.StringVar(value="Good")
        display_combo = ttk.Combobox(frame, textvariable=display_var,
                                    values=["Poor", "Fair", "Good", "Excellent"],
                                    state="readonly", width=27)
        display_combo.pack(pady=(0, 20), fill=tk.X)
        
        def save_info():
            self.evaluator_info = {
                'name': name_entry.get(),
                'age': age_entry.get(),
                'experience': experience_var.get(),
                'display_quality': display_var.get()
            }
            info_window.destroy()
        
        ttk.Button(frame, text="Start Evaluation", command=save_info).pack()

def prepare_evaluation_images(
    model_results_dir: str,
    categories: List[str] = None,
    max_images_per_category: int = 20
) -> List[Tuple[str, str, str]]:
    """
    Prepare image pairs for subjective evaluation
    
    Args:
        model_results_dir: Directory containing model evaluation results
        categories: List of categories to include
        max_images_per_category: Maximum images per category
        
    Returns:
        List of (original_path, enhanced_path, category) tuples
    """
    if categories is None:
        categories = ["text", "nature", "people", "animals", "games"]
    
    image_pairs = []
    
    for category in categories:
        category_dir = os.path.join(model_results_dir, category)
        if not os.path.exists(category_dir):
            continue
        
        # Look for comparison images or original/enhanced pairs
        images = [f for f in os.listdir(category_dir) if f.lower().endswith(('.png', '.jpg', '.jpeg'))]
        
        # Assume we have original and enhanced versions
        # This would need to be adapted based on your specific file structure
        for i, img_file in enumerate(images[:max_images_per_category]):
            if 'original' in img_file.lower():
                original_path = os.path.join(category_dir, img_file)
                enhanced_file = img_file.replace('original', 'enhanced')
                enhanced_path = os.path.join(category_dir, enhanced_file)
                
                if os.path.exists(enhanced_path):
                    image_pairs.append((original_path, enhanced_path, category))
    
    return image_pairs

def analyze_subjective_results(results_files: List[str]) -> Dict:
    """
    Analyze results from multiple evaluators
    
    Args:
        results_files: List of paths to evaluation result files
        
    Returns:
        Aggregated analysis results
    """
    all_scores = []
    category_scores = {}
    evaluator_stats = []
    
    for file_path in results_files:
        with open(file_path, 'r') as f:
            results = json.load(f)
        
        # Extract scores
        scores = [score['rating'] for score in results['scores'].values()]
        all_scores.extend(scores)
        
        # Category-wise scores
        for score_data in results['scores'].values():
            category = score_data['category']
            if category not in category_scores:
                category_scores[category] = []
            category_scores[category].append(score_data['rating'])
        
        # Evaluator statistics
        evaluator_stats.append({
            'mean_score': results['statistics']['mean_score'],
            'std_score': results['statistics']['std_score'],
            'experience': results['evaluator_info'].get('experience', 'Unknown')
        })
    
    # Calculate overall statistics
    overall_mos = np.mean(all_scores)
    overall_std = np.std(all_scores)
    
    # Category-wise MOS
    category_mos = {}
    for category, scores in category_scores.items():
        category_mos[category] = {
            'mos': np.mean(scores),
            'std': np.std(scores),
            'count': len(scores)
        }
    
    analysis = {
        'overall_mos': overall_mos,
        'overall_std': overall_std,
        'total_evaluations': len(all_scores),
        'num_evaluators': len(results_files),
        'category_mos': category_mos,
        'evaluator_stats': evaluator_stats,
        'score_distribution': {i: all_scores.count(i) for i in range(1, 6)}
    }
    
    return analysis

def run_subjective_study(
    model_results_dir: str,
    output_dir: str = "results/subjective_study",
    evaluator_id: str = None
) -> str:
    """
    Run subjective evaluation study
    
    Args:
        model_results_dir: Directory containing model results
        output_dir: Directory to save evaluation results
        evaluator_id: Unique identifier for evaluator
        
    Returns:
        Path to saved results
    """
    # Prepare image pairs
    image_pairs = prepare_evaluation_images(model_results_dir)
    
    if not image_pairs:
        raise ValueError("No image pairs found for evaluation")
    
    print(f"Prepared {len(image_pairs)} image pairs for evaluation")
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate output filename
    if evaluator_id is None:
        evaluator_id = f"evaluator_{len(os.listdir(output_dir)) + 1:03d}"
    
    output_file = os.path.join(output_dir, f"{evaluator_id}_results.json")
    
    # Run evaluation GUI
    evaluator = SubjectiveEvaluationGUI(image_pairs, output_file)
    evaluator.run()
    
    return output_file

if __name__ == "__main__":
    # Example usage
    import argparse
    
    parser = argparse.ArgumentParser(description="Run subjective evaluation study")
    parser.add_argument("--model_results_dir", type=str, required=True,
                       help="Directory containing model evaluation results")
    parser.add_argument("--output_dir", type=str, default="results/subjective_study",
                       help="Directory to save evaluation results")
    parser.add_argument("--evaluator_id", type=str, default=None,
                       help="Unique identifier for evaluator")
    
    args = parser.parse_args()
    
    try:
        result_file = run_subjective_study(
            args.model_results_dir,
            args.output_dir,
            args.evaluator_id
        )
        print(f"Subjective evaluation completed. Results saved to: {result_file}")
    except Exception as e:
        print(f"Error running subjective study: {e}")
