#!/usr/bin/env python3
"""
Complete example workflow for Image Sharpening Knowledge Distillation
"""

import os
import torch
from configs.config import config
from models.teacher.esrgan import create_esrgan_teacher, download_pretrained_weights
from models.student.lightweight_sr import create_student_model
from utils.dataset import create_dataloaders, download_sample_dataset
from training.trainer import KnowledgeDistillationTrainer
from evaluation.benchmark import run_comprehensive_evaluation
from utils.optimization import optimize_for_deployment

def main():
    """Complete example workflow"""
    print("="*60)
    print("Image Sharpening Knowledge Distillation - Complete Example")
    print("="*60)
    
    # Configuration
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    # Step 1: Create sample dataset
    print("\n1. Creating sample dataset...")
    data_dir = "data"
    download_sample_dataset(data_dir, num_samples=50)  # Small dataset for example
    
    # Step 2: Setup data loaders
    print("\n2. Setting up data loaders...")
    train_loader, val_loader, test_loader = create_dataloaders(
        train_dir=os.path.join(data_dir, 'train'),
        val_dir=os.path.join(data_dir, 'val'),
        test_dir=os.path.join(data_dir, 'test'),
        batch_size=4,  # Small batch size for example
        num_workers=2,
        input_size=(240, 135),  # Smaller size for faster training
        target_size=(960, 540),
        crop_size=(128, 128)
    )
    
    print(f"Data loaders created:")
    print(f"  Train: {len(train_loader)} batches")
    print(f"  Validation: {len(val_loader)} batches")
    print(f"  Test: {len(test_loader)} batches")
    
    # Step 3: Create teacher model
    print("\n3. Setting up teacher model...")
    try:
        # Try to download Real-ESRGAN pretrained weights
        teacher_weights = download_pretrained_weights('real_esrgan_x4')
        teacher = create_esrgan_teacher(scale=4, pretrained_path=teacher_weights)
        print("Using Real-ESRGAN as teacher model")
    except:
        # If download fails, try regular ESRGAN
        try:
            teacher_weights = download_pretrained_weights('esrgan_x4')
            teacher = create_esrgan_teacher(scale=4, pretrained_path=teacher_weights)
            print("Using ESRGAN as teacher model")
        except:
            # If all downloads fail, use random initialization
            print("Could not download pretrained weights, using random initialization")
            teacher = create_esrgan_teacher(scale=4, pretrained_path=None)
    
    teacher = teacher.to(device)
    print("Teacher model ready")
    
    # Step 4: Create student model
    print("\n4. Setting up student model...")
    student = create_student_model(model_type='ultra_light', scale_factor=4)
    student = student.to(device)
    
    # Step 5: Training
    print("\n5. Starting knowledge distillation training...")
    
    # Create trainer
    trainer = KnowledgeDistillationTrainer(
        teacher_model=teacher,
        student_model=student,
        train_loader=train_loader,
        val_loader=val_loader,
        device=device,
        checkpoint_dir='results/example/checkpoints',
        log_dir='results/example/logs'
    )
    
    # Train for 50 epochs (optimized for Kaggle)
    num_epochs = 50
    print(f"Training for {num_epochs} epochs...")
    trainer.train(num_epochs)
    
    # Step 6: Evaluation
    print("\n6. Evaluating trained model...")
    
    # Create test dataset directory structure for evaluation
    test_categories_dir = "data/test_categories"
    os.makedirs(os.path.join(test_categories_dir, "general"), exist_ok=True)
    
    # Copy some test images to the categories directory
    import shutil
    test_dir = os.path.join(data_dir, 'test')
    general_dir = os.path.join(test_categories_dir, "general")
    
    # Copy a few test images
    test_files = os.listdir(test_dir)[:5]  # Take first 5 images
    for file in test_files:
        src = os.path.join(test_dir, file)
        dst = os.path.join(general_dir, file)
        if not os.path.exists(dst):
            shutil.copy2(src, dst)
    
    # Run evaluation
    evaluation_results = run_comprehensive_evaluation(
        model=student,
        test_data_dir=test_categories_dir,
        device=device
    )
    
    # Step 7: Model optimization
    print("\n7. Optimizing model for deployment...")

    try:
        optimized_models = optimize_for_deployment(
            model=student,
            calibration_data=val_loader,
            output_dir='results/example/optimized',
            model_name='sharpening_model',
            quantize=True,
            convert_onnx=True,  # Will be skipped if ONNX not available
            prune=False  # Skip pruning for this example
        )
    except Exception as e:
        print(f"Optimization failed: {e}")
        print("Continuing without optimization...")
        optimized_models = {}
    
    # Step 8: Summary
    print("\n" + "="*60)
    print("EXAMPLE COMPLETED SUCCESSFULLY!")
    print("="*60)
    
    print("\nResults Summary:")
    if 'categories' in evaluation_results and 'overall' in evaluation_results['categories']:
        overall = evaluation_results['categories']['overall']
        print(f"  Overall SSIM: {overall['overall_ssim_mean']:.4f}")
        print(f"  Target achieved: {overall['target_ssim_achieved']}")
    
    if 'performance' in evaluation_results:
        perf = evaluation_results['performance']
        print(f"  Average FPS: {perf['fps']:.2f}")
    
    print(f"\nGenerated Files:")
    print(f"  Training logs: results/example/logs/")
    print(f"  Model checkpoints: results/example/checkpoints/")
    print(f"  Evaluation results: results/evaluation/")
    print(f"  Optimized models: results/example/optimized/")
    
    if optimized_models:
        print(f"\nOptimized Models:")
        for model_type, path in optimized_models.items():
            print(f"  {model_type}: {path}")
    else:
        print(f"\nNo optimized models generated (ONNX dependencies may be missing)")
    
    print("\nNext Steps:")
    print("1. Use demo.py to test the model interactively:")
    print("   python demo.py --model_path results/example/checkpoints/best_model.pth --mode webcam")
    print("\n2. Evaluate on your own dataset:")
    print("   python evaluate.py --model_path results/example/checkpoints/best_model.pth --test_data_dir your_data/")
    print("\n3. Train on a larger dataset:")
    print("   python train.py --data_dir your_large_dataset/ --epochs 100")
    
    print("\n" + "="*60)

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\nExample interrupted by user")
    except Exception as e:
        print(f"\nExample failed with error: {e}")
        import traceback
        traceback.print_exc()
