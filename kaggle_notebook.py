"""
Kaggle Notebook Template for Image Sharpening Knowledge Distillation

Copy this code into a Kaggle notebook for training.
Make sure to enable GPU acceleration in Kaggle settings.
"""

# Cell 1: Install dependencies
"""
!pip install lpips pytorch-msssim
"""

# Cell 2: Import libraries and setup
import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import torch.backends.cudnn as cudnn
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from torch.utils.tensorboard import SummaryWriter
import numpy as np
from PIL import Image, ImageFilter
import matplotlib.pyplot as plt
from tqdm import tqdm
import json
import time
import random

# Check GPU availability
device = 'cuda' if torch.cuda.is_available() else 'cpu'
print(f"Using device: {device}")
if device == 'cuda':
    print(f"GPU: {torch.cuda.get_device_name()}")
    print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    cudnn.benchmark = True

# Cell 3: Configuration
class Config:
    # Data configuration
    input_size = (240, 135)  # Low-res input
    target_size = (960, 540)  # Target output
    crop_size = (128, 128)   # Training crop size
    
    # Training configuration
    batch_size = 8
    num_epochs = 50
    learning_rate = 2e-4
    weight_decay = 1e-5
    
    # Knowledge distillation
    distillation_alpha = 0.7
    temperature = 4.0
    feature_loss_weight = 0.3
    attention_loss_weight = 0.2
    
    # Model configuration
    target_fps = 60
    max_parameters = 500_000  # 500K parameters for ultra-light model

config = Config()
print("Configuration loaded")

# Cell 4: Dataset class
class VideoConferencingDataset(Dataset):
    def __init__(self, image_dir, input_size=(240, 135), target_size=(960, 540), 
                 crop_size=(128, 128), augment=True):
        self.image_dir = image_dir
        self.input_size = input_size
        self.target_size = target_size
        self.crop_size = crop_size
        self.augment = augment
        
        # Get image files
        self.image_files = []
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp'}
        
        for file in os.listdir(image_dir):
            if any(file.lower().endswith(ext) for ext in valid_extensions):
                self.image_files.append(os.path.join(image_dir, file))
        
        print(f"Found {len(self.image_files)} images in {image_dir}")
        
        # Augmentation
        self.augment_transform = transforms.Compose([
            transforms.RandomRotation(5),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2),
            transforms.RandomHorizontalFlip(p=0.5),
        ]) if augment else None
    
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        # Load image
        img_path = self.image_files[idx]
        high_res = Image.open(img_path).convert('RGB')
        
        # Resize to target size
        if high_res.size != self.target_size:
            high_res = high_res.resize(self.target_size, Image.LANCZOS)
        
        # Random crop
        if self.crop_size:
            high_res = self._random_crop(high_res, self.crop_size)
            scale_x = self.crop_size[0] / self.target_size[0]
            scale_y = self.crop_size[1] / self.target_size[1]
            crop_input_size = (int(self.input_size[0] * scale_x), int(self.input_size[1] * scale_y))
        else:
            crop_input_size = self.input_size
        
        # Simulate video conferencing conditions
        low_res = high_res.resize(crop_input_size, Image.BICUBIC)
        bicubic_upscaled = low_res.resize(high_res.size, Image.BICUBIC)
        
        # Apply augmentation
        if self.augment_transform and self.augment:
            seed = random.randint(0, 2**32 - 1)
            
            random.seed(seed)
            torch.manual_seed(seed)
            high_res = self.augment_transform(high_res)
            
            random.seed(seed)
            torch.manual_seed(seed)
            low_res = self.augment_transform(low_res)
            
            random.seed(seed)
            torch.manual_seed(seed)
            bicubic_upscaled = self.augment_transform(bicubic_upscaled)
        
        # Convert to tensors
        to_tensor = transforms.ToTensor()
        
        return {
            'low_res': to_tensor(low_res),
            'high_res': to_tensor(high_res),
            'bicubic_upscaled': to_tensor(bicubic_upscaled),
            'filename': os.path.basename(img_path)
        }
    
    def _random_crop(self, img, crop_size):
        w, h = img.size
        crop_w, crop_h = crop_size
        
        if w < crop_w or h < crop_h:
            scale = max(crop_w / w, crop_h / h)
            new_w, new_h = int(w * scale), int(h * scale)
            img = img.resize((new_w, new_h), Image.LANCZOS)
            w, h = new_w, new_h
        
        left = random.randint(0, w - crop_w)
        top = random.randint(0, h - crop_h)
        
        return img.crop((left, top, left + crop_w, top + crop_h))

print("Dataset class defined")

# Cell 5: Create sample dataset
def create_sample_dataset(data_dir="data", num_samples=200):
    """Create sample dataset for training"""
    print(f"Creating sample dataset in {data_dir}")
    
    for split in ['train', 'val', 'test']:
        split_dir = os.path.join(data_dir, split)
        os.makedirs(split_dir, exist_ok=True)
        
        if split == 'train':
            n_samples = int(num_samples * 0.8)
        elif split == 'val':
            n_samples = int(num_samples * 0.1)
        else:
            n_samples = int(num_samples * 0.1)
        
        print(f"Creating {n_samples} images for {split}")
        
        for i in range(n_samples):
            # Create random image with patterns
            img = Image.new('RGB', config.target_size, 
                          color=(random.randint(0, 255), random.randint(0, 255), random.randint(0, 255)))
            
            # Add texture
            img_array = np.array(img)
            noise = np.random.randint(0, 50, img_array.shape, dtype=np.uint8)
            img_array = np.clip(img_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            img = Image.fromarray(img_array)
            img.save(os.path.join(split_dir, f"sample_{i:04d}.png"))
    
    print("Sample dataset created!")

# Create dataset
create_sample_dataset("/kaggle/working/data", num_samples=200)

# Cell 6: Student model definition
class DepthwiseSeparableConv(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3, stride=1, padding=1):
        super().__init__()
        self.depthwise = nn.Conv2d(in_channels, in_channels, kernel_size, stride, padding, groups=in_channels)
        self.pointwise = nn.Conv2d(in_channels, out_channels, 1)
    
    def forward(self, x):
        x = self.depthwise(x)
        x = self.pointwise(x)
        return x

class MobileBlock(nn.Module):
    def __init__(self, channels, expand_ratio=2):
        super().__init__()
        expanded_channels = channels * expand_ratio
        
        self.expand = nn.Conv2d(channels, expanded_channels, 1) if expand_ratio > 1 else nn.Identity()
        self.bn1 = nn.BatchNorm2d(expanded_channels) if expand_ratio > 1 else nn.Identity()
        
        self.depthwise = nn.Conv2d(expanded_channels, expanded_channels, 3, 1, 1, groups=expanded_channels)
        self.bn2 = nn.BatchNorm2d(expanded_channels)
        
        self.project = nn.Conv2d(expanded_channels, channels, 1)
        self.bn3 = nn.BatchNorm2d(channels)
        
        self.activation = nn.ReLU6(inplace=True)
    
    def forward(self, x):
        identity = x
        
        out = self.expand(x)
        if not isinstance(self.bn1, nn.Identity):
            out = self.bn1(out)
            out = self.activation(out)
        
        out = self.depthwise(out)
        out = self.bn2(out)
        out = self.activation(out)
        
        out = self.project(out)
        out = self.bn3(out)
        
        return out + identity

class UltraLightStudent(nn.Module):
    def __init__(self, num_channels=16, num_blocks=2, scale_factor=4):
        super().__init__()
        self.scale_factor = scale_factor
        
        # Initial conv
        self.conv_first = nn.Conv2d(3, num_channels, 3, 1, 1)
        
        # Feature blocks
        self.feature_blocks = nn.ModuleList([
            MobileBlock(num_channels) for _ in range(num_blocks)
        ])
        
        # Feature fusion
        self.conv_fusion = nn.Conv2d(num_channels, num_channels, 1)
        
        # Upsampling
        self.upsample = nn.Sequential(
            nn.Conv2d(num_channels, num_channels * 4, 3, 1, 1),
            nn.PixelShuffle(2),
            nn.ReLU(inplace=True),
            nn.Conv2d(num_channels, 3 * 4, 3, 1, 1),
            nn.PixelShuffle(2)
        )
        
        self.activation = nn.ReLU(inplace=True)
        self.intermediate_features = {}
        self._register_hooks()
    
    def _register_hooks(self):
        def get_activation(name):
            def hook(model, input, output):
                self.intermediate_features[name] = output
            return hook
        
        self.conv_first.register_forward_hook(get_activation('conv_first'))
        for i, block in enumerate(self.feature_blocks):
            block.register_forward_hook(get_activation(f'feature_block_{i}'))
        self.conv_fusion.register_forward_hook(get_activation('conv_fusion'))
    
    def forward(self, x):
        self.intermediate_features.clear()
        
        feat = self.activation(self.conv_first(x))
        
        for block in self.feature_blocks:
            feat = feat + block(feat)
        
        feat = self.conv_fusion(feat)
        out = self.upsample(feat)
        
        # Skip connection
        x_upsampled = torch.nn.functional.interpolate(x, scale_factor=self.scale_factor, mode='bicubic', align_corners=False)
        out = out + x_upsampled
        
        return out
    
    def get_features(self):
        return self.intermediate_features.copy()
    
    def count_parameters(self):
        return sum(p.numel() for p in self.parameters() if p.requires_grad)

print("Student model defined")

# Cell 7: Create models and data loaders
# Create student model
student = UltraLightStudent().to(device)
print(f"Student model: {student.count_parameters():,} parameters")

# Create data loaders
train_dataset = VideoConferencingDataset("/kaggle/working/data/train", augment=True)
val_dataset = VideoConferencingDataset("/kaggle/working/data/val", augment=False)

train_loader = DataLoader(train_dataset, batch_size=config.batch_size, shuffle=True, num_workers=2)
val_loader = DataLoader(val_dataset, batch_size=config.batch_size, shuffle=False, num_workers=2)

print(f"Data loaders ready: {len(train_loader)} train batches, {len(val_loader)} val batches")

# Cell 8: Training loop (simplified)
def train_model():
    # Simple training without teacher (for demonstration)
    # In practice, you would load a pre-trained teacher model
    
    optimizer = optim.Adam(student.parameters(), lr=config.learning_rate, weight_decay=config.weight_decay)
    criterion = nn.L1Loss()
    
    best_loss = float('inf')
    
    for epoch in range(config.num_epochs):
        student.train()
        epoch_loss = 0
        
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{config.num_epochs}')
        for batch in pbar:
            low_res = batch['low_res'].to(device)
            high_res = batch['high_res'].to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            enhanced = student(low_res)
            loss = criterion(enhanced, high_res)
            
            # Backward pass
            loss.backward()
            optimizer.step()
            
            epoch_loss += loss.item()
            pbar.set_postfix({'loss': f'{loss.item():.4f}'})
        
        avg_loss = epoch_loss / len(train_loader)
        print(f'Epoch {epoch+1}: Average Loss = {avg_loss:.4f}')
        
        # Save best model
        if avg_loss < best_loss:
            best_loss = avg_loss
            torch.save({
                'model_state_dict': student.state_dict(),
                'epoch': epoch,
                'loss': avg_loss,
                'config': {
                    'type': 'ultra_light',
                    'parameters': student.count_parameters()
                }
            }, '/kaggle/working/best_model.pth')
            print(f'New best model saved (loss: {avg_loss:.4f})')
    
    print("Training completed!")
    return student

# Start training
trained_model = train_model()

# Cell 9: Performance evaluation
def evaluate_performance(model, num_iterations=100):
    model.eval()
    
    # Create dummy input
    dummy_input = torch.randn(1, 3, config.target_size[1], config.target_size[0]).to(device)
    
    # Warmup
    with torch.no_grad():
        for _ in range(10):
            _ = model(dummy_input)
    
    # Measure performance
    times = []
    with torch.no_grad():
        for _ in tqdm(range(num_iterations), desc="Performance test"):
            start_time = time.time()
            _ = model(dummy_input)
            torch.cuda.synchronize() if device == 'cuda' else None
            end_time = time.time()
            times.append((end_time - start_time) * 1000)  # Convert to ms
    
    times = np.array(times)
    fps = 1000.0 / np.mean(times)
    
    results = {
        'mean_time_ms': np.mean(times),
        'std_time_ms': np.std(times),
        'fps': fps,
        'target_achieved': fps >= 30
    }
    
    print(f"Performance Results:")
    print(f"  Average FPS: {fps:.2f}")
    print(f"  Average time: {np.mean(times):.2f} ms")
    print(f"  Target (30+ FPS): {'✓' if fps >= 30 else '✗'}")
    
    return results

# Evaluate performance
perf_results = evaluate_performance(trained_model)

# Save results
with open('/kaggle/working/performance_results.json', 'w') as f:
    json.dump(perf_results, f, indent=2)

print("\n🎉 Kaggle training completed!")
print("Download the 'best_model.pth' file from the output to use your trained model.")
