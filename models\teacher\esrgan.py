"""
ESRGAN Teacher Model Implementation
Enhanced Super-Resolution Generative Adversarial Networks
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import List, Tuple
import math

class ResidualDenseBlock(nn.Module):
    """Residual Dense Block used in ESRGAN"""
    
    def __init__(self, num_feat=64, num_grow_ch=32):
        super(ResidualDenseBlock, self).__init__()
        self.conv1 = nn.Conv2d(num_feat, num_grow_ch, 3, 1, 1)
        self.conv2 = nn.Conv2d(num_feat + num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv3 = nn.Conv2d(num_feat + 2 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv4 = nn.Conv2d(num_feat + 3 * num_grow_ch, num_grow_ch, 3, 1, 1)
        self.conv5 = nn.Conv2d(num_feat + 4 * num_grow_ch, num_feat, 3, 1, 1)
        
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)
        
        # Initialization
        self._initialize_weights()
    
    def _initialize_weights(self):
        for m in [self.conv1, self.conv2, self.conv3, self.conv4]:
            nn.init.kaiming_normal_(m.weight, a=0.2, mode='fan_in', nonlinearity='leaky_relu')
            nn.init.constant_(m.bias, 0)
        nn.init.kaiming_normal_(self.conv5.weight, a=0.2, mode='fan_in', nonlinearity='leaky_relu')
        nn.init.constant_(self.conv5.bias, 0)
    
    def forward(self, x):
        x1 = self.lrelu(self.conv1(x))
        x2 = self.lrelu(self.conv2(torch.cat((x, x1), 1)))
        x3 = self.lrelu(self.conv3(torch.cat((x, x1, x2), 1)))
        x4 = self.lrelu(self.conv4(torch.cat((x, x1, x2, x3), 1)))
        x5 = self.conv5(torch.cat((x, x1, x2, x3, x4), 1))
        # Empirical scaling factor 0.2
        return x5 * 0.2 + x

class RRDB(nn.Module):
    """Residual in Residual Dense Block"""
    
    def __init__(self, num_feat, num_grow_ch=32):
        super(RRDB, self).__init__()
        self.rdb1 = ResidualDenseBlock(num_feat, num_grow_ch)
        self.rdb2 = ResidualDenseBlock(num_feat, num_grow_ch)
        self.rdb3 = ResidualDenseBlock(num_feat, num_grow_ch)
    
    def forward(self, x):
        out = self.rdb1(x)
        out = self.rdb2(out)
        out = self.rdb3(out)
        # Empirical scaling factor 0.2
        return out * 0.2 + x

class ESRGANGenerator(nn.Module):
    """ESRGAN Generator Network"""
    
    def __init__(
        self,
        num_in_ch=3,
        num_out_ch=3,
        scale=4,
        num_feat=64,
        num_block=23,
        num_grow_ch=32
    ):
        super(ESRGANGenerator, self).__init__()
        self.scale = scale
        
        # First convolution
        self.conv_first = nn.Conv2d(num_in_ch, num_feat, 3, 1, 1)
        
        # Body: RRDB blocks
        self.body = nn.ModuleList()
        for _ in range(num_block):
            self.body.append(RRDB(num_feat, num_grow_ch))
        
        # Body convolution
        self.conv_body = nn.Conv2d(num_feat, num_feat, 3, 1, 1)
        
        # Upsampling
        self.upsampler = self._make_upsampler(scale, num_feat)
        
        # Final convolution
        self.conv_last = nn.Conv2d(num_feat, num_out_ch, 3, 1, 1)
        
        self.lrelu = nn.LeakyReLU(negative_slope=0.2, inplace=True)
    
    def _make_upsampler(self, scale, num_feat):
        """Create upsampling layers"""
        layers = []
        
        if scale == 2 or scale == 4 or scale == 8:
            for _ in range(int(math.log(scale, 2))):
                layers.extend([
                    nn.Conv2d(num_feat, num_feat * 4, 3, 1, 1),
                    nn.PixelShuffle(2),
                    nn.LeakyReLU(negative_slope=0.2, inplace=True)
                ])
        elif scale == 3:
            layers.extend([
                nn.Conv2d(num_feat, num_feat * 9, 3, 1, 1),
                nn.PixelShuffle(3),
                nn.LeakyReLU(negative_slope=0.2, inplace=True)
            ])
        else:
            raise ValueError(f'Unsupported scale: {scale}')
        
        return nn.Sequential(*layers)
    
    def forward(self, x):
        # Extract features
        feat = self.conv_first(x)
        
        # Body
        body_feat = feat
        for block in self.body:
            body_feat = block(body_feat)
        body_feat = self.conv_body(body_feat)
        
        # Add skip connection
        feat = feat + body_feat
        
        # Upsample
        feat = self.upsampler(feat)
        
        # Final output
        out = self.conv_last(feat)
        
        return out

class ESRGANTeacher(nn.Module):
    """ESRGAN Teacher Model for Knowledge Distillation"""
    
    def __init__(
        self,
        scale=4,
        num_feat=64,
        num_block=23,
        pretrained_path=None
    ):
        super(ESRGANTeacher, self).__init__()
        
        self.generator = ESRGANGenerator(
            num_in_ch=3,
            num_out_ch=3,
            scale=scale,
            num_feat=num_feat,
            num_block=num_block
        )
        
        # Load pretrained weights if provided
        if pretrained_path:
            self.load_pretrained(pretrained_path)
        
        # Set to evaluation mode for teacher
        self.eval()
        
        # Store intermediate features for knowledge distillation
        self.intermediate_features = {}
        self._register_hooks()
    
    def _register_hooks(self):
        """Register hooks to extract intermediate features"""
        def get_activation(name):
            def hook(model, input, output):
                self.intermediate_features[name] = output
            return hook
        
        # Register hooks at different depths
        self.generator.conv_first.register_forward_hook(get_activation('conv_first'))
        
        # Register hooks for some RRDB blocks
        for i, block in enumerate(self.generator.body):
            if i % 5 == 0:  # Every 5th block
                block.register_forward_hook(get_activation(f'rrdb_{i}'))
        
        self.generator.conv_body.register_forward_hook(get_activation('conv_body'))
        self.generator.upsampler.register_forward_hook(get_activation('upsampler'))
    
    def forward(self, x):
        """Forward pass with feature extraction"""
        self.intermediate_features.clear()
        output = self.generator(x)
        return output
    
    def get_features(self) -> dict:
        """Get intermediate features for knowledge distillation"""
        return self.intermediate_features.copy()
    
    def load_pretrained(self, path):
        """Load pretrained weights"""
        try:
            checkpoint = torch.load(path, map_location='cpu')
            
            # Handle different checkpoint formats
            if 'state_dict' in checkpoint:
                state_dict = checkpoint['state_dict']
            elif 'model' in checkpoint:
                state_dict = checkpoint['model']
            else:
                state_dict = checkpoint
            
            # Remove 'module.' prefix if present (from DataParallel)
            new_state_dict = {}
            for k, v in state_dict.items():
                if k.startswith('module.'):
                    new_state_dict[k[7:]] = v
                else:
                    new_state_dict[k] = v
            
            self.generator.load_state_dict(new_state_dict, strict=False)
            print(f"Loaded pretrained ESRGAN weights from {path}")
            
        except Exception as e:
            print(f"Warning: Could not load pretrained weights from {path}: {e}")
            print("Continuing with random initialization...")

def create_esrgan_teacher(
    scale: int = 4,
    pretrained_path: str = None
) -> ESRGANTeacher:
    """
    Create ESRGAN teacher model
    
    Args:
        scale: Upscaling factor
        pretrained_path: Path to pretrained weights
        
    Returns:
        ESRGANTeacher model
    """
    model = ESRGANTeacher(
        scale=scale,
        pretrained_path=pretrained_path
    )
    
    return model

# Model URLs for downloading pretrained weights
PRETRAINED_URLS = {
    'esrgan_x4': 'https://github.com/xinntao/ESRGAN/releases/download/v0.0.0/RRDB_ESRGAN_x4.pth',
    'real_esrgan_x4': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.1.0/RealESRGAN_x4plus.pth',
    'real_esrgan_x4_v3': 'https://github.com/xinntao/Real-ESRGAN/releases/download/v0.2.5.0/realesr-general-x4v3.pth'
}

def download_pretrained_weights(model_name: str, save_dir: str = "models/pretrained"):
    """Download pretrained weights"""
    import os
    import urllib.request
    
    if model_name not in PRETRAINED_URLS:
        raise ValueError(f"Unknown model: {model_name}. Available: {list(PRETRAINED_URLS.keys())}")
    
    os.makedirs(save_dir, exist_ok=True)
    url = PRETRAINED_URLS[model_name]
    filename = os.path.join(save_dir, f"{model_name}.pth")
    
    if not os.path.exists(filename):
        print(f"Downloading {model_name} weights...")
        urllib.request.urlretrieve(url, filename)
        print(f"Downloaded to {filename}")
    else:
        print(f"Weights already exist at {filename}")
    
    return filename
