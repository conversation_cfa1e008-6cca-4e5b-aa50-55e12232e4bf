#!/usr/bin/env python3
"""
Main training script for Image Sharpening Knowledge Distillation
"""

import os
import argparse
import torch
import torch.backends.cudnn as cudnn

from configs.config import config
from models.teacher.esrgan import create_esrgan_teacher, download_pretrained_weights
from models.student.lightweight_sr import create_student_model
from utils.dataset import create_dataloaders, download_sample_dataset
from training.trainer import KnowledgeDistillationTrainer

def parse_args():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(description='Train Image Sharpening Model with Knowledge Distillation')
    
    # Model arguments
    parser.add_argument('--student_type', type=str, default='balanced',
                       choices=['ultra_light', 'balanced', 'custom'],
                       help='Type of student model')
    parser.add_argument('--teacher_model', type=str, default='esrgan',
                       choices=['esrgan'],
                       help='Teacher model type')
    parser.add_argument('--teacher_weights', type=str, default=None,
                       help='Path to teacher model weights')
    
    # Training arguments
    parser.add_argument('--epochs', type=int, default=100,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=16,
                       help='Batch size for training')
    parser.add_argument('--lr', type=float, default=1e-4,
                       help='Learning rate')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (cuda/cpu/auto)')
    
    # Data arguments
    parser.add_argument('--data_dir', type=str, default='data',
                       help='Directory containing training data')
    parser.add_argument('--create_sample_data', action='store_true',
                       help='Create sample dataset for testing')
    parser.add_argument('--num_samples', type=int, default=100,
                       help='Number of sample images to create')
    
    # Experiment arguments
    parser.add_argument('--experiment_name', type=str, default='default',
                       help='Name of the experiment')
    parser.add_argument('--resume', type=str, default=None,
                       help='Path to checkpoint to resume from')
    
    return parser.parse_args()

def setup_device(device_arg):
    """Setup device for training"""
    if device_arg == 'auto':
        device = 'cuda' if torch.cuda.is_available() else 'cpu'
    else:
        device = device_arg
    
    if device == 'cuda':
        if not torch.cuda.is_available():
            print("CUDA not available, falling back to CPU")
            device = 'cpu'
        else:
            print(f"Using GPU: {torch.cuda.get_device_name()}")
            cudnn.benchmark = True
    else:
        print("Using CPU")
    
    return device

def create_teacher_model(args, device):
    """Create and load teacher model"""
    print("Setting up teacher model...")
    
    if args.teacher_model == 'esrgan':
        # Download pretrained weights if not provided
        if args.teacher_weights is None:
            print("Downloading pretrained ESRGAN weights...")
            args.teacher_weights = download_pretrained_weights('esrgan_x4')
        
        teacher = create_esrgan_teacher(
            scale=4,
            pretrained_path=args.teacher_weights
        )
    else:
        raise ValueError(f"Unknown teacher model: {args.teacher_model}")
    
    teacher = teacher.to(device)
    print(f"Teacher model loaded successfully")
    
    return teacher

def create_student_model_from_args(args, device):
    """Create student model from arguments"""
    print("Setting up student model...")
    
    if args.student_type == 'custom':
        # Custom model parameters can be added here
        student = create_student_model(
            model_type='custom',
            scale_factor=4,
            num_channels=32,
            num_blocks=4,
            use_se=True,
            use_depthwise=True
        )
    else:
        student = create_student_model(
            model_type=args.student_type,
            scale_factor=4
        )
    
    student = student.to(device)
    return student

def setup_data(args):
    """Setup data loaders"""
    print("Setting up data loaders...")
    
    # Create sample dataset if requested
    if args.create_sample_data:
        print("Creating sample dataset...")
        download_sample_dataset(args.data_dir, args.num_samples)
    
    # Check if data directories exist
    train_dir = os.path.join(args.data_dir, 'train')
    val_dir = os.path.join(args.data_dir, 'val')
    test_dir = os.path.join(args.data_dir, 'test')
    
    for dir_path, dir_name in [(train_dir, 'train'), (val_dir, 'val'), (test_dir, 'test')]:
        if not os.path.exists(dir_path) or len(os.listdir(dir_path)) == 0:
            raise ValueError(f"{dir_name} directory is empty or doesn't exist: {dir_path}")
    
    # Create data loaders
    train_loader, val_loader, test_loader = create_dataloaders(
        train_dir=train_dir,
        val_dir=val_dir,
        test_dir=test_dir,
        batch_size=args.batch_size,
        num_workers=4,
        input_size=config.data.input_size,
        target_size=config.data.target_size,
        crop_size=config.data.crop_size
    )
    
    print(f"Data loaders created:")
    print(f"  Train: {len(train_loader)} batches")
    print(f"  Validation: {len(val_loader)} batches")
    print(f"  Test: {len(test_loader)} batches")
    
    return train_loader, val_loader, test_loader

def main():
    """Main training function"""
    args = parse_args()
    
    print("="*50)
    print("Image Sharpening Knowledge Distillation Training")
    print("="*50)
    
    # Setup device
    device = setup_device(args.device)
    
    # Update config with command line arguments
    config.training.batch_size = args.batch_size
    config.training.learning_rate = args.lr
    config.training.num_epochs = args.epochs
    
    # Setup experiment directories
    experiment_dir = os.path.join('results', args.experiment_name)
    checkpoint_dir = os.path.join(experiment_dir, 'checkpoints')
    log_dir = os.path.join(experiment_dir, 'logs')
    
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Setup data
    train_loader, val_loader, test_loader = setup_data(args)
    
    # Create models
    teacher = create_teacher_model(args, device)
    student = create_student_model_from_args(args, device)
    
    # Create trainer
    trainer = KnowledgeDistillationTrainer(
        teacher_model=teacher,
        student_model=student,
        train_loader=train_loader,
        val_loader=val_loader,
        device=device,
        checkpoint_dir=checkpoint_dir,
        log_dir=log_dir
    )
    
    # Resume from checkpoint if specified
    if args.resume:
        trainer.load_checkpoint(args.resume)
    
    # Start training
    print(f"\nStarting training for {args.epochs} epochs...")
    print(f"Experiment: {args.experiment_name}")
    print(f"Device: {device}")
    print(f"Student model: {args.student_type}")
    print(f"Teacher model: {args.teacher_model}")
    
    try:
        trainer.train(args.epochs)
        print("Training completed successfully!")
        
        # Save final model
        final_model_path = os.path.join(checkpoint_dir, 'final_model.pth')
        torch.save({
            'model_state_dict': student.state_dict(),
            'model_config': {
                'type': args.student_type,
                'scale_factor': 4
            }
        }, final_model_path)
        print(f"Final model saved to {final_model_path}")
        
    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
        # Save current state
        interrupt_path = os.path.join(checkpoint_dir, 'interrupted_model.pth')
        torch.save({
            'model_state_dict': student.state_dict(),
            'epoch': trainer.current_epoch,
            'optimizer_state_dict': trainer.optimizer.state_dict()
        }, interrupt_path)
        print(f"Model state saved to {interrupt_path}")
    
    except Exception as e:
        print(f"Training failed with error: {e}")
        raise

if __name__ == '__main__':
    main()
