#!/usr/bin/env python3
"""
Kaggle-optimized training script for Image Sharpening Knowledge Distillation
Designed to work efficiently in Kaggle environment with limited time
"""

import os
import torch
import torch.backends.cudnn as cudnn
from configs.config import config
from models.teacher.esrgan import create_esrgan_teacher, download_pretrained_weights
from models.student.lightweight_sr import create_student_model
from utils.dataset import create_dataloaders, download_sample_dataset
from training.trainer import KnowledgeDistillationTrainer

def main():
    """Kaggle-optimized training"""
    print("="*60)
    print("Image Sharpening Knowledge Distillation - Kaggle Training")
    print("="*60)
    
    # Kaggle environment setup
    device = 'cuda' if torch.cuda.is_available() else 'cpu'
    print(f"Using device: {device}")
    
    if device == 'cuda':
        print(f"GPU: {torch.cuda.get_device_name()}")
        print(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
        cudnn.benchmark = True
    
    # Optimized configuration for Kaggle
    config.training.batch_size = 8  # Smaller batch size for memory efficiency
    config.training.num_epochs = 50  # 50 epochs for good results
    config.training.learning_rate = 2e-4  # Slightly higher LR for faster convergence
    config.data.crop_size = (128, 128)  # Smaller crops for faster training
    config.data.input_size = (240, 135)  # Smaller input size
    config.data.target_size = (960, 540)  # Smaller target size
    
    print("Kaggle-optimized configuration:")
    print(f"  Batch size: {config.training.batch_size}")
    print(f"  Epochs: {config.training.num_epochs}")
    print(f"  Learning rate: {config.training.learning_rate}")
    print(f"  Crop size: {config.data.crop_size}")
    
    # Step 1: Create dataset
    print("\n1. Creating dataset...")
    data_dir = "/kaggle/working/data"
    
    # Check if we're in Kaggle environment
    if os.path.exists("/kaggle/input"):
        print("Kaggle environment detected")
        # In Kaggle, you would typically have your dataset in /kaggle/input/
        # For this example, we'll create a sample dataset
        download_sample_dataset(data_dir, num_samples=200)  # Small dataset for demo
    else:
        # Local environment
        download_sample_dataset(data_dir, num_samples=200)
    
    # Step 2: Setup data loaders
    print("\n2. Setting up data loaders...")
    train_loader, val_loader, test_loader = create_dataloaders(
        train_dir=os.path.join(data_dir, 'train'),
        val_dir=os.path.join(data_dir, 'val'),
        test_dir=os.path.join(data_dir, 'test'),
        batch_size=config.training.batch_size,
        num_workers=2,  # Limited workers for Kaggle
        input_size=config.data.input_size,
        target_size=config.data.target_size,
        crop_size=config.data.crop_size
    )
    
    print(f"Data loaders ready:")
    print(f"  Train: {len(train_loader)} batches")
    print(f"  Validation: {len(val_loader)} batches")
    
    # Step 3: Create teacher model
    print("\n3. Setting up teacher model...")
    try:
        # Try Real-ESRGAN first
        print("Downloading Real-ESRGAN weights...")
        teacher_weights = download_pretrained_weights('real_esrgan_x4')
        teacher = create_esrgan_teacher(scale=4, pretrained_path=teacher_weights)
        print("✓ Real-ESRGAN teacher model loaded")
    except Exception as e:
        print(f"Real-ESRGAN download failed: {e}")
        try:
            # Fallback to ESRGAN
            print("Trying ESRGAN weights...")
            teacher_weights = download_pretrained_weights('esrgan_x4')
            teacher = create_esrgan_teacher(scale=4, pretrained_path=teacher_weights)
            print("✓ ESRGAN teacher model loaded")
        except Exception as e2:
            print(f"ESRGAN download failed: {e2}")
            print("Using randomly initialized teacher (not recommended)")
            teacher = create_esrgan_teacher(scale=4, pretrained_path=None)
    
    teacher = teacher.to(device)
    
    # Step 4: Create student model
    print("\n4. Setting up student model...")
    # Use ultra-light model for fastest training and inference
    student = create_student_model(model_type='ultra_light', scale_factor=4)
    student = student.to(device)
    
    print(f"Student model: {student.count_parameters():,} parameters")
    print(f"Model size: {student.get_model_size_mb():.2f} MB")
    print(f"Target FPS: {config.model.target_fps}")
    
    # Step 5: Training
    print("\n5. Starting knowledge distillation training...")
    
    # Create output directories
    experiment_dir = "/kaggle/working/results"
    checkpoint_dir = os.path.join(experiment_dir, 'checkpoints')
    log_dir = os.path.join(experiment_dir, 'logs')
    
    os.makedirs(checkpoint_dir, exist_ok=True)
    os.makedirs(log_dir, exist_ok=True)
    
    # Create trainer
    trainer = KnowledgeDistillationTrainer(
        teacher_model=teacher,
        student_model=student,
        train_loader=train_loader,
        val_loader=val_loader,
        device=device,
        checkpoint_dir=checkpoint_dir,
        log_dir=log_dir
    )
    
    # Start training
    print(f"Training for {config.training.num_epochs} epochs...")
    print("This will take approximately 30-60 minutes on Kaggle GPU")
    
    try:
        trainer.train(config.training.num_epochs)
        print("✓ Training completed successfully!")
        
        # Save final model
        final_model_path = os.path.join(checkpoint_dir, 'final_model.pth')
        torch.save({
            'model_state_dict': student.state_dict(),
            'model_config': {
                'type': 'ultra_light',
                'scale_factor': 4,
                'parameters': student.count_parameters(),
                'model_size_mb': student.get_model_size_mb()
            },
            'training_config': {
                'epochs': config.training.num_epochs,
                'batch_size': config.training.batch_size,
                'learning_rate': config.training.learning_rate
            }
        }, final_model_path)
        print(f"✓ Final model saved: {final_model_path}")
        
        # Quick evaluation
        print("\n6. Quick evaluation...")
        from evaluation.benchmark import BenchmarkEvaluator
        
        evaluator = BenchmarkEvaluator(student, device, save_results=False)
        
        # Performance test
        perf_results = evaluator.evaluate_performance(
            input_size=(960, 540),  # Test on smaller resolution for speed
            num_iterations=50
        )
        
        print(f"\nPerformance Results:")
        print(f"  Average FPS: {perf_results['fps']:.2f}")
        print(f"  Inference time: {perf_results['mean_time_ms']:.2f} ms")
        print(f"  Target achieved: {'✓' if perf_results['fps'] >= 30 else '✗'}")
        
        # Save performance results
        import json
        perf_path = os.path.join(experiment_dir, 'performance_results.json')
        with open(perf_path, 'w') as f:
            json.dump(perf_results, f, indent=2)
        
        print(f"\n{'='*60}")
        print("KAGGLE TRAINING COMPLETED SUCCESSFULLY!")
        print(f"{'='*60}")
        print(f"Best model: {os.path.join(checkpoint_dir, 'best_model.pth')}")
        print(f"Final model: {final_model_path}")
        print(f"Performance: {perf_results['fps']:.2f} FPS")
        print(f"Model size: {student.get_model_size_mb():.2f} MB")
        print(f"Parameters: {student.count_parameters():,}")
        
        # Instructions for download
        print(f"\nTo download your trained model:")
        print(f"1. Go to Kaggle Output tab")
        print(f"2. Download the entire 'results' folder")
        print(f"3. Use the model with: python demo.py --model_path results/checkpoints/best_model.pth")
        
        return True
        
    except KeyboardInterrupt:
        print("\nTraining interrupted by user")
        # Save current state
        interrupt_path = os.path.join(checkpoint_dir, 'interrupted_model.pth')
        torch.save({
            'model_state_dict': student.state_dict(),
            'epoch': trainer.current_epoch,
            'optimizer_state_dict': trainer.optimizer.state_dict()
        }, interrupt_path)
        print(f"Model state saved: {interrupt_path}")
        return False
        
    except Exception as e:
        print(f"Training failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    try:
        success = main()
        if success:
            print("\n🎉 Training completed successfully!")
        else:
            print("\n❌ Training failed or was interrupted")
    except Exception as e:
        print(f"\n💥 Fatal error: {e}")
        import traceback
        traceback.print_exc()
