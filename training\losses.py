"""
Loss functions for knowledge distillation training
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import lpips
from typing import Dict, List, Tuple

class DistillationLoss(nn.Module):
    """Combined loss for knowledge distillation"""
    
    def __init__(
        self,
        alpha=0.7,
        temperature=4.0,
        pixel_loss_weight=1.0,
        feature_loss_weight=0.3,
        attention_loss_weight=0.2,
        perceptual_loss_weight=0.1,
        device='cuda'
    ):
        super(DistillationLoss, self).__init__()
        
        self.alpha = alpha  # Weight for distillation vs ground truth
        self.temperature = temperature
        self.pixel_loss_weight = pixel_loss_weight
        self.feature_loss_weight = feature_loss_weight
        self.attention_loss_weight = attention_loss_weight
        self.perceptual_loss_weight = perceptual_loss_weight
        
        # Loss functions
        self.l1_loss = nn.L1Loss()
        self.mse_loss = nn.MSELoss()
        
        # Perceptual loss (LPIPS)
        if perceptual_loss_weight > 0:
            self.perceptual_loss = lpips.LPIPS(net='alex').to(device)
        else:
            self.perceptual_loss = None
    
    def forward(
        self,
        student_output: torch.Tensor,
        teacher_output: torch.Tensor,
        ground_truth: torch.Tensor,
        student_features: Dict[str, torch.Tensor],
        teacher_features: Dict[str, torch.Tensor]
    ) -> Dict[str, torch.Tensor]:
        """
        Calculate combined distillation loss
        
        Args:
            student_output: Student model output
            teacher_output: Teacher model output  
            ground_truth: Ground truth high-resolution image
            student_features: Student intermediate features
            teacher_features: Teacher intermediate features
            
        Returns:
            Dictionary of losses
        """
        losses = {}
        
        # 1. Pixel-level losses
        # Student vs Ground Truth
        pixel_loss_gt = self.l1_loss(student_output, ground_truth)
        
        # Student vs Teacher (soft targets)
        pixel_loss_teacher = self.l1_loss(student_output, teacher_output)
        
        # Combined pixel loss
        pixel_loss = (1 - self.alpha) * pixel_loss_gt + self.alpha * pixel_loss_teacher
        losses['pixel_loss'] = pixel_loss * self.pixel_loss_weight
        
        # 2. Feature distillation loss
        feature_loss = self._calculate_feature_loss(student_features, teacher_features)
        losses['feature_loss'] = feature_loss * self.feature_loss_weight
        
        # 3. Attention transfer loss
        attention_loss = self._calculate_attention_loss(student_features, teacher_features)
        losses['attention_loss'] = attention_loss * self.attention_loss_weight
        
        # 4. Perceptual loss
        if self.perceptual_loss is not None:
            # Normalize to [-1, 1] for LPIPS
            student_norm = student_output * 2 - 1
            gt_norm = ground_truth * 2 - 1
            
            perceptual_loss = self.perceptual_loss(student_norm, gt_norm).mean()
            losses['perceptual_loss'] = perceptual_loss * self.perceptual_loss_weight
        
        # Total loss
        total_loss = sum(losses.values())
        losses['total_loss'] = total_loss
        
        return losses
    
    def _calculate_feature_loss(
        self,
        student_features: Dict[str, torch.Tensor],
        teacher_features: Dict[str, torch.Tensor]
    ) -> torch.Tensor:
        """Calculate feature matching loss"""
        feature_loss = 0.0
        count = 0
        
        for name in student_features:
            if name in teacher_features:
                s_feat = student_features[name]
                t_feat = teacher_features[name]
                
                # Adapt feature dimensions if needed
                if s_feat.shape != t_feat.shape:
                    # Use adaptive pooling to match spatial dimensions
                    if s_feat.shape[2:] != t_feat.shape[2:]:
                        s_feat = F.adaptive_avg_pool2d(s_feat, t_feat.shape[2:])
                    
                    # Use 1x1 conv to match channel dimensions
                    if s_feat.shape[1] != t_feat.shape[1]:
                        # Simple channel adaptation (could be learned)
                        if s_feat.shape[1] < t_feat.shape[1]:
                            # Pad channels
                            pad_channels = t_feat.shape[1] - s_feat.shape[1]
                            s_feat = F.pad(s_feat, (0, 0, 0, 0, 0, pad_channels))
                        else:
                            # Average pool channels
                            s_feat = F.adaptive_avg_pool1d(
                                s_feat.view(s_feat.shape[0], s_feat.shape[1], -1),
                                t_feat.shape[1]
                            ).view(s_feat.shape[0], t_feat.shape[1], s_feat.shape[2], s_feat.shape[3])
                
                # Calculate L2 loss between features
                feature_loss += self.mse_loss(s_feat, t_feat.detach())
                count += 1
        
        return feature_loss / max(count, 1)
    
    def _calculate_attention_loss(
        self,
        student_features: Dict[str, torch.Tensor],
        teacher_features: Dict[str, torch.Tensor]
    ) -> torch.Tensor:
        """Calculate attention transfer loss"""
        attention_loss = 0.0
        count = 0
        
        for name in student_features:
            if name in teacher_features:
                s_feat = student_features[name]
                t_feat = teacher_features[name]
                
                # Calculate attention maps (spatial attention)
                s_attention = self._get_attention_map(s_feat)
                t_attention = self._get_attention_map(t_feat)
                
                # Match spatial dimensions
                if s_attention.shape != t_attention.shape:
                    s_attention = F.interpolate(
                        s_attention, size=t_attention.shape[2:], 
                        mode='bilinear', align_corners=False
                    )
                
                # Calculate attention loss
                attention_loss += self.mse_loss(s_attention, t_attention.detach())
                count += 1
        
        return attention_loss / max(count, 1)
    
    def _get_attention_map(self, feature: torch.Tensor) -> torch.Tensor:
        """Generate attention map from feature"""
        # Sum across channels and normalize
        attention = torch.sum(feature.abs(), dim=1, keepdim=True)
        
        # Normalize to [0, 1]
        b, c, h, w = attention.shape
        attention = attention.view(b, c, -1)
        attention = attention / (attention.max(dim=2, keepdim=True)[0] + 1e-8)
        attention = attention.view(b, c, h, w)
        
        return attention

class AdversarialLoss(nn.Module):
    """Adversarial loss for GAN-based training (optional)"""
    
    def __init__(self, loss_type='lsgan'):
        super(AdversarialLoss, self).__init__()
        
        self.loss_type = loss_type
        
        if loss_type == 'lsgan':
            self.criterion = nn.MSELoss()
        elif loss_type == 'vanilla':
            self.criterion = nn.BCEWithLogitsLoss()
        else:
            raise ValueError(f"Unsupported loss type: {loss_type}")
    
    def forward(self, pred, target_is_real):
        """Calculate adversarial loss"""
        if self.loss_type == 'lsgan':
            target = torch.ones_like(pred) if target_is_real else torch.zeros_like(pred)
        else:  # vanilla
            target = torch.ones_like(pred) if target_is_real else torch.zeros_like(pred)
        
        return self.criterion(pred, target)

class SSIMLoss(nn.Module):
    """SSIM Loss for structural similarity"""
    
    def __init__(self, window_size=11, size_average=True):
        super(SSIMLoss, self).__init__()
        self.window_size = window_size
        self.size_average = size_average
        
    def forward(self, img1, img2):
        """Calculate SSIM loss (1 - SSIM)"""
        ssim_value = self._ssim(img1, img2)
        return 1 - ssim_value
    
    def _ssim(self, img1, img2):
        """Calculate SSIM"""
        # Simplified SSIM calculation
        mu1 = F.avg_pool2d(img1, self.window_size, 1, self.window_size//2)
        mu2 = F.avg_pool2d(img2, self.window_size, 1, self.window_size//2)
        
        mu1_sq = mu1.pow(2)
        mu2_sq = mu2.pow(2)
        mu1_mu2 = mu1 * mu2
        
        sigma1_sq = F.avg_pool2d(img1 * img1, self.window_size, 1, self.window_size//2) - mu1_sq
        sigma2_sq = F.avg_pool2d(img2 * img2, self.window_size, 1, self.window_size//2) - mu2_sq
        sigma12 = F.avg_pool2d(img1 * img2, self.window_size, 1, self.window_size//2) - mu1_mu2
        
        C1 = 0.01 ** 2
        C2 = 0.03 ** 2
        
        ssim_map = ((2 * mu1_mu2 + C1) * (2 * sigma12 + C2)) / ((mu1_sq + mu2_sq + C1) * (sigma1_sq + sigma2_sq + C2))
        
        if self.size_average:
            return ssim_map.mean()
        else:
            return ssim_map.mean(1).mean(1).mean(1)

def create_loss_function(
    loss_config: dict,
    device: str = 'cuda'
) -> DistillationLoss:
    """Create loss function from configuration"""

    return DistillationLoss(
        alpha=loss_config.get('alpha', 0.7),
        temperature=loss_config.get('temperature', 4.0),
        pixel_loss_weight=loss_config.get('pixel_loss_weight', 1.0),
        feature_loss_weight=loss_config.get('feature_loss_weight', 0.3),
        attention_loss_weight=loss_config.get('attention_loss_weight', 0.2),
        perceptual_loss_weight=loss_config.get('perceptual_loss_weight', 0.1),
        device=device
    )
