"""
Knowledge Distillation Trainer for Image Sharpening
"""

import os
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torch.utils.tensorboard import SummaryWriter
from typing import Dict, Tuple, Optional
import numpy as np
from tqdm import tqdm

from configs.config import config
from training.losses import create_loss_function
from utils.metrics import ImageMetrics, PerformanceTimer

class KnowledgeDistillationTrainer:
    """Trainer for knowledge distillation"""
    
    def __init__(
        self,
        teacher_model: nn.Module,
        student_model: nn.Module,
        train_loader: DataLoader,
        val_loader: DataLoader,
        device: str = 'cuda',
        checkpoint_dir: str = 'results/checkpoints',
        log_dir: str = 'results/logs'
    ):
        self.teacher_model = teacher_model.to(device)
        self.student_model = student_model.to(device)
        self.train_loader = train_loader
        self.val_loader = val_loader
        self.device = device
        
        # Set teacher to eval mode
        self.teacher_model.eval()
        for param in self.teacher_model.parameters():
            param.requires_grad = False
        
        # Create directories
        os.makedirs(checkpoint_dir, exist_ok=True)
        os.makedirs(log_dir, exist_ok=True)
        self.checkpoint_dir = checkpoint_dir
        
        # Initialize loss function
        loss_config = {
            'alpha': config.training.distillation_alpha,
            'temperature': config.training.temperature,
            'feature_loss_weight': config.training.feature_loss_weight,
            'attention_loss_weight': config.training.attention_loss_weight,
            'perceptual_loss_weight': config.training.perceptual_loss_weight
        }
        self.criterion = create_loss_function(loss_config, device)
        
        # Initialize optimizer
        self.optimizer = self._create_optimizer()
        self.scheduler = self._create_scheduler()
        
        # Initialize metrics
        self.metrics = ImageMetrics(device)
        self.timer = PerformanceTimer()
        
        # Initialize logging
        self.writer = SummaryWriter(log_dir)
        
        # Training state
        self.current_epoch = 0
        self.best_ssim = 0.0
        self.training_history = {'train_loss': [], 'val_ssim': [], 'val_loss': []}
    
    def _create_optimizer(self):
        """Create optimizer"""
        if config.training.optimizer == 'Adam':
            return optim.Adam(
                self.student_model.parameters(),
                lr=config.training.learning_rate,
                weight_decay=config.training.weight_decay
            )
        elif config.training.optimizer == 'AdamW':
            return optim.AdamW(
                self.student_model.parameters(),
                lr=config.training.learning_rate,
                weight_decay=config.training.weight_decay
            )
        else:
            return optim.SGD(
                self.student_model.parameters(),
                lr=config.training.learning_rate,
                momentum=0.9,
                weight_decay=config.training.weight_decay
            )
    
    def _create_scheduler(self):
        """Create learning rate scheduler"""
        if config.training.scheduler == 'CosineAnnealingLR':
            return optim.lr_scheduler.CosineAnnealingLR(
                self.optimizer, T_max=config.training.num_epochs
            )
        elif config.training.scheduler == 'StepLR':
            return optim.lr_scheduler.StepLR(
                self.optimizer, step_size=30, gamma=0.1
            )
        else:
            return optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='max', factor=0.5, patience=10
            )
    
    def train_epoch(self) -> Dict[str, float]:
        """Train for one epoch"""
        self.student_model.train()
        
        epoch_losses = {'total_loss': 0, 'pixel_loss': 0, 'feature_loss': 0, 
                       'attention_loss': 0, 'perceptual_loss': 0}
        num_batches = len(self.train_loader)
        
        pbar = tqdm(self.train_loader, desc=f'Epoch {self.current_epoch}')
        
        for batch_idx, batch in enumerate(pbar):
            # Move data to device
            low_res = batch['low_res'].to(self.device)
            high_res = batch['high_res'].to(self.device)
            
            # Forward pass through teacher (no gradients)
            with torch.no_grad():
                teacher_output = self.teacher_model(low_res)
                teacher_features = self.teacher_model.get_features()
            
            # Forward pass through student
            student_output = self.student_model(low_res)
            student_features = self.student_model.get_features()
            
            # Calculate loss
            losses = self.criterion(
                student_output, teacher_output, high_res,
                student_features, teacher_features
            )
            
            # Backward pass
            self.optimizer.zero_grad()
            losses['total_loss'].backward()
            
            # Gradient clipping
            torch.nn.utils.clip_grad_norm_(self.student_model.parameters(), max_norm=1.0)
            
            self.optimizer.step()
            
            # Accumulate losses
            for key, value in losses.items():
                if key in epoch_losses:
                    epoch_losses[key] += value.item()
            
            # Update progress bar
            pbar.set_postfix({
                'loss': f"{losses['total_loss'].item():.4f}",
                'lr': f"{self.optimizer.param_groups[0]['lr']:.6f}"
            })
        
        # Average losses
        for key in epoch_losses:
            epoch_losses[key] /= num_batches
        
        return epoch_losses
    
    def validate(self) -> Dict[str, float]:
        """Validate the model"""
        self.student_model.eval()
        
        val_losses = {'total_loss': 0, 'pixel_loss': 0, 'feature_loss': 0, 
                     'attention_loss': 0, 'perceptual_loss': 0}
        ssim_scores = []
        psnr_scores = []
        
        with torch.no_grad():
            for batch in tqdm(self.val_loader, desc='Validation'):
                low_res = batch['low_res'].to(self.device)
                high_res = batch['high_res'].to(self.device)
                
                # Forward pass through teacher
                teacher_output = self.teacher_model(low_res)
                teacher_features = self.teacher_model.get_features()
                
                # Forward pass through student
                student_output = self.student_model(low_res)
                student_features = self.student_model.get_features()
                
                # Calculate loss
                losses = self.criterion(
                    student_output, teacher_output, high_res,
                    student_features, teacher_features
                )
                
                # Accumulate losses
                for key, value in losses.items():
                    if key in val_losses:
                        val_losses[key] += value.item()
                
                # Calculate metrics
                for i in range(student_output.shape[0]):
                    student_img = student_output[i].cpu().numpy().transpose(1, 2, 0)
                    gt_img = high_res[i].cpu().numpy().transpose(1, 2, 0)
                    
                    # Clip to [0, 1]
                    student_img = np.clip(student_img, 0, 1)
                    gt_img = np.clip(gt_img, 0, 1)
                    
                    ssim_score = self.metrics.calculate_ssim(student_img, gt_img)
                    psnr_score = self.metrics.calculate_psnr(student_img, gt_img)
                    
                    ssim_scores.append(ssim_score)
                    psnr_scores.append(psnr_score)
        
        # Average losses and metrics
        num_batches = len(self.val_loader)
        for key in val_losses:
            val_losses[key] /= num_batches
        
        val_metrics = {
            'ssim': np.mean(ssim_scores),
            'psnr': np.mean(psnr_scores),
            'ssim_std': np.std(ssim_scores),
            'psnr_std': np.std(psnr_scores)
        }
        
        return {**val_losses, **val_metrics}
    
    def train(self, num_epochs: int):
        """Main training loop"""
        print(f"Starting training for {num_epochs} epochs...")
        print(f"Student model parameters: {self.student_model.count_parameters():,}")
        print(f"Student model size: {self.student_model.get_model_size_mb():.2f} MB")
        
        for epoch in range(num_epochs):
            self.current_epoch = epoch
            
            # Train
            train_losses = self.train_epoch()
            
            # Validate
            val_results = self.validate()
            
            # Update scheduler
            if isinstance(self.scheduler, optim.lr_scheduler.ReduceLROnPlateau):
                self.scheduler.step(val_results['ssim'])
            else:
                self.scheduler.step()
            
            # Log results
            self._log_results(train_losses, val_results)
            
            # Save checkpoint
            if (epoch + 1) % config.training.save_every == 0:
                self._save_checkpoint(val_results['ssim'])
            
            # Save best model
            if val_results['ssim'] > self.best_ssim:
                self.best_ssim = val_results['ssim']
                self._save_checkpoint(val_results['ssim'], is_best=True)
            
            # Update history
            self.training_history['train_loss'].append(train_losses['total_loss'])
            self.training_history['val_ssim'].append(val_results['ssim'])
            self.training_history['val_loss'].append(val_results['total_loss'])
            
            print(f"Epoch {epoch+1}/{num_epochs} - "
                  f"Train Loss: {train_losses['total_loss']:.4f}, "
                  f"Val SSIM: {val_results['ssim']:.4f}, "
                  f"Val PSNR: {val_results['psnr']:.2f}")
        
        print(f"Training completed! Best SSIM: {self.best_ssim:.4f}")
        self.writer.close()
    
    def _log_results(self, train_losses: Dict[str, float], val_results: Dict[str, float]):
        """Log results to tensorboard"""
        epoch = self.current_epoch
        
        # Log training losses
        for key, value in train_losses.items():
            self.writer.add_scalar(f'Train/{key}', value, epoch)
        
        # Log validation results
        for key, value in val_results.items():
            self.writer.add_scalar(f'Validation/{key}', value, epoch)
        
        # Log learning rate
        self.writer.add_scalar('Learning_Rate', self.optimizer.param_groups[0]['lr'], epoch)
    
    def _save_checkpoint(self, ssim_score: float, is_best: bool = False):
        """Save model checkpoint"""
        checkpoint = {
            'epoch': self.current_epoch,
            'student_state_dict': self.student_model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_ssim': self.best_ssim,
            'ssim_score': ssim_score,
            'training_history': self.training_history
        }
        
        # Save regular checkpoint
        checkpoint_path = os.path.join(
            self.checkpoint_dir, f'checkpoint_epoch_{self.current_epoch+1}.pth'
        )
        torch.save(checkpoint, checkpoint_path)
        
        # Save best model
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)
            print(f"New best model saved with SSIM: {ssim_score:.4f}")
    
    def load_checkpoint(self, checkpoint_path: str):
        """Load checkpoint"""
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        self.student_model.load_state_dict(checkpoint['student_state_dict'])
        self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        self.current_epoch = checkpoint['epoch']
        self.best_ssim = checkpoint['best_ssim']
        self.training_history = checkpoint['training_history']
        
        print(f"Loaded checkpoint from epoch {self.current_epoch+1}")
        print(f"Best SSIM so far: {self.best_ssim:.4f}")
