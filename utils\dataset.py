"""
Dataset utilities for image sharpening knowledge distillation
"""

import os
import random
from typing import <PERSON>ple, List, Optional, Callable
import torch
from torch.utils.data import Dataset, DataLoader
import torchvision.transforms as transforms
from PIL import Image, ImageFilter
import numpy as np

class VideoConferencingDataset(Dataset):
    """
    Dataset that simulates video conferencing conditions by downscaling/upscaling images
    """
    
    def __init__(
        self,
        image_dir: str,
        input_size: Tuple[int, int] = (480, 270),
        target_size: Tuple[int, int] = (1920, 1080),
        crop_size: Optional[Tuple[int, int]] = None,
        transform: Optional[Callable] = None,
        augment: bool = True
    ):
        """
        Args:
            image_dir: Directory containing high-resolution images
            input_size: Low-resolution size to simulate poor connection
            target_size: Target high-resolution size
            crop_size: Size to crop images for training efficiency
            transform: Additional transforms to apply
            augment: Whether to apply data augmentation
        """
        self.image_dir = image_dir
        self.input_size = input_size
        self.target_size = target_size
        self.crop_size = crop_size
        self.transform = transform
        self.augment = augment
        
        # Get all image files
        self.image_files = []
        valid_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        
        for file in os.listdir(image_dir):
            if any(file.lower().endswith(ext) for ext in valid_extensions):
                self.image_files.append(os.path.join(image_dir, file))
        
        if not self.image_files:
            raise ValueError(f"No valid images found in {image_dir}")
        
        # Define augmentation transforms
        self.augment_transform = transforms.Compose([
            transforms.RandomRotation(5),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.RandomHorizontalFlip(p=0.5),
        ]) if augment else None
        
    def __len__(self):
        return len(self.image_files)
    
    def __getitem__(self, idx):
        """
        Returns:
            dict with keys:
                - 'low_res': Low-resolution input image (simulating poor connection)
                - 'high_res': High-resolution target image
                - 'bicubic_upscaled': Bicubic upscaled version of low_res
        """
        # Load high-resolution image
        img_path = self.image_files[idx]
        high_res = Image.open(img_path).convert('RGB')
        
        # Resize to target size if needed
        if high_res.size != self.target_size:
            high_res = high_res.resize(self.target_size, Image.LANCZOS)
        
        # Apply random crop if specified (for training efficiency)
        if self.crop_size:
            high_res = self._random_crop(high_res, self.crop_size)
            # Adjust input size proportionally
            scale_x = self.crop_size[0] / self.target_size[0]
            scale_y = self.crop_size[1] / self.target_size[1]
            crop_input_size = (
                int(self.input_size[0] * scale_x),
                int(self.input_size[1] * scale_y)
            )
        else:
            crop_input_size = self.input_size
        
        # Simulate video conferencing conditions
        # 1. Downscale to simulate low bandwidth
        low_res = high_res.resize(crop_input_size, Image.BICUBIC)
        
        # 2. Upscale back using bicubic interpolation (baseline method)
        bicubic_upscaled = low_res.resize(high_res.size, Image.BICUBIC)
        
        # Apply augmentation to all images consistently
        if self.augment_transform and self.augment:
            # Use same random state for all images to maintain consistency
            seed = random.randint(0, 2**32 - 1)
            
            random.seed(seed)
            torch.manual_seed(seed)
            high_res = self.augment_transform(high_res)
            
            random.seed(seed)
            torch.manual_seed(seed)
            low_res = self.augment_transform(low_res)
            
            random.seed(seed)
            torch.manual_seed(seed)
            bicubic_upscaled = self.augment_transform(bicubic_upscaled)
        
        # Convert to tensors
        to_tensor = transforms.ToTensor()
        
        sample = {
            'low_res': to_tensor(low_res),
            'high_res': to_tensor(high_res),
            'bicubic_upscaled': to_tensor(bicubic_upscaled),
            'filename': os.path.basename(img_path)
        }
        
        # Apply additional transforms if provided
        if self.transform:
            sample = self.transform(sample)
        
        return sample
    
    def _random_crop(self, img: Image.Image, crop_size: Tuple[int, int]) -> Image.Image:
        """Apply random crop to image"""
        w, h = img.size
        crop_w, crop_h = crop_size
        
        if w < crop_w or h < crop_h:
            # Resize if image is smaller than crop size
            scale = max(crop_w / w, crop_h / h)
            new_w, new_h = int(w * scale), int(h * scale)
            img = img.resize((new_w, new_h), Image.LANCZOS)
            w, h = new_w, new_h
        
        # Random crop
        left = random.randint(0, w - crop_w)
        top = random.randint(0, h - crop_h)
        
        return img.crop((left, top, left + crop_w, top + crop_h))

def create_sharpening_augmentation():
    """Create augmentation that includes sharpening effects"""
    def sharpen_and_augment(sample):
        """Apply sharpening and additional augmentation"""
        high_res = sample['high_res']
        
        # Convert tensor back to PIL for sharpening
        to_pil = transforms.ToPILImage()
        to_tensor = transforms.ToTensor()
        
        high_res_pil = to_pil(high_res)
        
        # Apply unsharp mask for sharpening
        sharpened = high_res_pil.filter(
            ImageFilter.UnsharpMask(radius=2, percent=150, threshold=3)
        )
        
        sample['high_res'] = to_tensor(sharpened)
        return sample
    
    return sharpen_and_augment

def create_dataloaders(
    train_dir: str,
    val_dir: str,
    test_dir: str,
    batch_size: int = 16,
    num_workers: int = 4,
    input_size: Tuple[int, int] = (480, 270),
    target_size: Tuple[int, int] = (1920, 1080),
    crop_size: Optional[Tuple[int, int]] = (256, 256)
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """
    Create train, validation, and test dataloaders
    
    Returns:
        Tuple of (train_loader, val_loader, test_loader)
    """
    
    # Training dataset with augmentation
    train_dataset = VideoConferencingDataset(
        train_dir,
        input_size=input_size,
        target_size=target_size,
        crop_size=crop_size,
        augment=True,
        transform=create_sharpening_augmentation()
    )
    
    # Validation dataset without augmentation
    val_dataset = VideoConferencingDataset(
        val_dir,
        input_size=input_size,
        target_size=target_size,
        crop_size=crop_size,
        augment=False
    )
    
    # Test dataset without augmentation
    test_dataset = VideoConferencingDataset(
        test_dir,
        input_size=input_size,
        target_size=target_size,
        crop_size=None,  # Use full resolution for testing
        augment=False
    )
    
    # Create dataloaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=1,  # Process one image at a time for testing
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available()
    )
    
    return train_loader, val_loader, test_loader

def download_sample_dataset(data_dir: str = "data", num_samples: int = 100):
    """
    Download or create a sample dataset for testing
    This is a placeholder - in practice, you would download from a dataset like DIV2K
    """
    print(f"Creating sample dataset in {data_dir}")
    
    # Create sample images (placeholder)
    # In practice, you would download from datasets like:
    # - DIV2K: https://data.vision.ee.ethz.ch/cvl/DIV2K/
    # - Flickr2K: https://cv.snu.ac.kr/research/EDSR/Flickr2K.tar
    # - Set5, Set14, BSD100 for testing
    
    for split in ['train', 'val', 'test']:
        split_dir = os.path.join(data_dir, split)
        os.makedirs(split_dir, exist_ok=True)
        
        # Determine number of samples for each split
        if split == 'train':
            n_samples = int(num_samples * 0.8)
        elif split == 'val':
            n_samples = int(num_samples * 0.1)
        else:  # test
            n_samples = int(num_samples * 0.1)
        
        print(f"Creating {n_samples} sample images for {split} set")
        
        # Create sample images (random colored patterns)
        for i in range(n_samples):
            # Create a random image
            img = Image.new('RGB', (1920, 1080), 
                          color=(random.randint(0, 255), 
                                random.randint(0, 255), 
                                random.randint(0, 255)))
            
            # Add some patterns to make it more interesting
            import numpy as np
            img_array = np.array(img)
            
            # Add some texture
            noise = np.random.randint(0, 50, img_array.shape, dtype=np.uint8)
            img_array = np.clip(img_array.astype(np.int16) + noise, 0, 255).astype(np.uint8)
            
            img = Image.fromarray(img_array)
            img.save(os.path.join(split_dir, f"sample_{i:04d}.png"))
    
    print("Sample dataset created successfully!")
    print("Note: For real training, please use high-quality datasets like DIV2K or Flickr2K")
