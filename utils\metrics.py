"""
Evaluation metrics for image sharpening
"""

import torch
import torch.nn.functional as F
import numpy as np
from skimage.metrics import structural_similarity as ssim
from skimage.metrics import peak_signal_noise_ratio as psnr
from PIL import Image
import lpips

class ImageMetrics:
    """Collection of image quality metrics"""
    
    def __init__(self, device='cuda'):
        self.device = device
        # Initialize LPIPS model for perceptual similarity
        self.lpips_model = lpips.LPIPS(net='alex').to(device)
        
    def calculate_ssim(self, img1, img2, data_range=1.0):
        """
        Calculate Structural Similarity Index (SSIM)
        
        Args:
            img1, img2: Images as numpy arrays or PIL Images
            data_range: Data range of the images (1.0 for [0,1], 255 for [0,255])
        
        Returns:
            SSIM score (float)
        """
        # Convert to numpy if needed
        if isinstance(img1, Image.Image):
            img1 = np.array(img1)
        if isinstance(img2, Image.Image):
            img2 = np.array(img2)
            
        # Ensure images are in the same format
        if img1.dtype != img2.dtype:
            img1 = img1.astype(np.float32)
            img2 = img2.astype(np.float32)
            
        # Calculate SSIM
        if len(img1.shape) == 3:  # Color image
            ssim_score = ssim(img1, img2, data_range=data_range, multichannel=True, channel_axis=2)
        else:  # Grayscale
            ssim_score = ssim(img1, img2, data_range=data_range)
            
        return ssim_score
    
    def calculate_psnr(self, img1, img2, data_range=1.0):
        """
        Calculate Peak Signal-to-Noise Ratio (PSNR)
        
        Args:
            img1, img2: Images as numpy arrays or PIL Images
            data_range: Data range of the images
            
        Returns:
            PSNR score (float)
        """
        # Convert to numpy if needed
        if isinstance(img1, Image.Image):
            img1 = np.array(img1)
        if isinstance(img2, Image.Image):
            img2 = np.array(img2)
            
        return psnr(img1, img2, data_range=data_range)
    
    def calculate_lpips(self, img1, img2):
        """
        Calculate Learned Perceptual Image Patch Similarity (LPIPS)
        
        Args:
            img1, img2: Torch tensors in range [-1, 1] with shape (B, C, H, W)
            
        Returns:
            LPIPS score (float)
        """
        with torch.no_grad():
            lpips_score = self.lpips_model(img1, img2)
        return lpips_score.mean().item()
    
    def calculate_mse(self, img1, img2):
        """Calculate Mean Squared Error"""
        if isinstance(img1, Image.Image):
            img1 = np.array(img1)
        if isinstance(img2, Image.Image):
            img2 = np.array(img2)
            
        return np.mean((img1.astype(np.float32) - img2.astype(np.float32)) ** 2)
    
    def calculate_mae(self, img1, img2):
        """Calculate Mean Absolute Error"""
        if isinstance(img1, Image.Image):
            img1 = np.array(img1)
        if isinstance(img2, Image.Image):
            img2 = np.array(img2)
            
        return np.mean(np.abs(img1.astype(np.float32) - img2.astype(np.float32)))

def tensor_to_numpy(tensor):
    """Convert torch tensor to numpy array for metric calculation"""
    if isinstance(tensor, torch.Tensor):
        # Move to CPU and convert to numpy
        tensor = tensor.detach().cpu()
        if tensor.dim() == 4:  # Batch dimension
            tensor = tensor.squeeze(0)
        if tensor.dim() == 3:  # CHW to HWC
            tensor = tensor.permute(1, 2, 0)
        return tensor.numpy()
    return tensor

def numpy_to_pil(array):
    """Convert numpy array to PIL Image"""
    if array.dtype != np.uint8:
        # Assume array is in [0, 1] range
        array = (array * 255).astype(np.uint8)
    
    if len(array.shape) == 3 and array.shape[2] == 3:
        return Image.fromarray(array, 'RGB')
    elif len(array.shape) == 2:
        return Image.fromarray(array, 'L')
    else:
        raise ValueError(f"Unsupported array shape: {array.shape}")

class PerformanceTimer:
    """Timer for measuring inference performance"""
    
    def __init__(self):
        self.times = []
        
    def start(self):
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        self.start_time = torch.cuda.Event(enable_timing=True) if torch.cuda.is_available() else None
        if self.start_time:
            self.start_time.record()
        else:
            import time
            self.start_time = time.time()
    
    def end(self):
        if torch.cuda.is_available():
            torch.cuda.synchronize()
            end_time = torch.cuda.Event(enable_timing=True)
            end_time.record()
            torch.cuda.synchronize()
            elapsed = self.start_time.elapsed_time(end_time)  # milliseconds
        else:
            import time
            elapsed = (time.time() - self.start_time) * 1000  # convert to milliseconds
            
        self.times.append(elapsed)
        return elapsed
    
    def get_fps(self):
        """Calculate average FPS from recorded times"""
        if not self.times:
            return 0
        avg_time_ms = np.mean(self.times)
        return 1000.0 / avg_time_ms  # Convert ms to FPS
    
    def get_stats(self):
        """Get timing statistics"""
        if not self.times:
            return {}
        
        times = np.array(self.times)
        return {
            'mean_ms': np.mean(times),
            'std_ms': np.std(times),
            'min_ms': np.min(times),
            'max_ms': np.max(times),
            'fps': 1000.0 / np.mean(times)
        }
