"""
Model optimization utilities for deployment
"""

import os
import torch
import torch.nn as nn
import torch.quantization as quantization
import numpy as np
from typing import Tuple, Optional, Dict

# Optional imports for ONNX functionality
try:
    import onnx
    import onnxruntime as ort
    ONNX_AVAILABLE = True
except ImportError:
    ONNX_AVAILABLE = False
    print("Warning: ONNX not available. ONNX conversion will be skipped.")

class ModelOptimizer:
    """Utilities for model optimization and deployment"""
    
    def __init__(self, model: nn.Module, device: str = 'cuda'):
        self.model = model
        self.device = device
        
    def quantize_model(
        self,
        calibration_data: torch.utils.data.DataLoader,
        quantization_type: str = 'dynamic'
    ) -> nn.Module:
        """
        Quantize model for faster inference
        
        Args:
            calibration_data: DataLoader for calibration (for static quantization)
            quantization_type: 'dynamic' or 'static'
            
        Returns:
            Quantized model
        """
        print(f"Applying {quantization_type} quantization...")
        
        if quantization_type == 'dynamic':
            # Dynamic quantization (post-training)
            quantized_model = torch.quantization.quantize_dynamic(
                self.model.cpu(),
                {nn.Conv2d, nn.Linear},
                dtype=torch.qint8
            )
            
        elif quantization_type == 'static':
            # Static quantization (requires calibration)
            self.model.cpu()
            self.model.eval()
            
            # Set quantization config
            self.model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
            
            # Prepare model for quantization
            prepared_model = torch.quantization.prepare(self.model)
            
            # Calibrate with sample data
            print("Calibrating model...")
            with torch.no_grad():
                for i, batch in enumerate(calibration_data):
                    if i >= 100:  # Use first 100 batches for calibration
                        break
                    
                    low_res = batch['low_res']
                    _ = prepared_model(low_res)
            
            # Convert to quantized model
            quantized_model = torch.quantization.convert(prepared_model)
            
        else:
            raise ValueError(f"Unknown quantization type: {quantization_type}")
        
        # Calculate size reduction
        original_size = self._get_model_size(self.model)
        quantized_size = self._get_model_size(quantized_model)
        reduction = (1 - quantized_size / original_size) * 100
        
        print(f"Model size reduced by {reduction:.1f}% ({original_size:.2f} MB -> {quantized_size:.2f} MB)")
        
        return quantized_model
    
    def convert_to_onnx(
        self,
        output_path: str,
        input_size: Tuple[int, int] = (1920, 1080),
        opset_version: int = 11,
        optimize: bool = True
    ) -> str:
        """
        Convert model to ONNX format

        Args:
            output_path: Path to save ONNX model
            input_size: Input image size (width, height)
            opset_version: ONNX opset version
            optimize: Whether to optimize the ONNX model

        Returns:
            Path to saved ONNX model
        """
        if not ONNX_AVAILABLE:
            print("ONNX not available, skipping ONNX conversion")
            return output_path

        print(f"Converting model to ONNX format...")

        self.model.eval()

        # Create dummy input
        dummy_input = torch.randn(1, 3, input_size[1], input_size[0])

        # Export to ONNX
        torch.onnx.export(
            self.model,
            dummy_input,
            output_path,
            export_params=True,
            opset_version=opset_version,
            do_constant_folding=True,
            input_names=['input'],
            output_names=['output'],
            dynamic_axes={
                'input': {0: 'batch_size', 2: 'height', 3: 'width'},
                'output': {0: 'batch_size', 2: 'height', 3: 'width'}
            }
        )

        # Optimize ONNX model if requested
        if optimize:
            optimized_path = output_path.replace('.onnx', '_optimized.onnx')
            self._optimize_onnx_model(output_path, optimized_path)
            output_path = optimized_path

        # Verify ONNX model
        self._verify_onnx_model(output_path, dummy_input)

        print(f"ONNX model saved to: {output_path}")
        return output_path
    
    def _optimize_onnx_model(self, input_path: str, output_path: str):
        """Optimize ONNX model"""
        if not ONNX_AVAILABLE:
            print("ONNX not available, skipping optimization")
            return

        try:
            import onnxoptimizer

            # Load model
            model = onnx.load(input_path)

            # Apply optimizations
            optimized_model = onnxoptimizer.optimize(model)

            # Save optimized model
            onnx.save(optimized_model, output_path)

            print(f"ONNX model optimized and saved to: {output_path}")

        except ImportError:
            print("onnxoptimizer not available, skipping optimization")
            # Just copy the original file
            import shutil
            shutil.copy2(input_path, output_path)
    
    def _verify_onnx_model(self, onnx_path: str, dummy_input: torch.Tensor):
        """Verify ONNX model correctness"""
        if not ONNX_AVAILABLE:
            print("ONNX not available, skipping verification")
            return

        print("Verifying ONNX model...")

        # Load ONNX model
        onnx_model = onnx.load(onnx_path)
        onnx.checker.check_model(onnx_model)

        # Create ONNX Runtime session
        ort_session = ort.InferenceSession(onnx_path)

        # Run inference with PyTorch
        self.model.eval()
        with torch.no_grad():
            torch_output = self.model(dummy_input)

        # Run inference with ONNX Runtime
        ort_inputs = {ort_session.get_inputs()[0].name: dummy_input.numpy()}
        ort_output = ort_session.run(None, ort_inputs)[0]

        # Compare outputs
        torch_output_np = torch_output.numpy()
        max_diff = np.max(np.abs(torch_output_np - ort_output))

        if max_diff < 1e-5:
            print(f"ONNX model verification passed (max diff: {max_diff:.2e})")
        else:
            print(f"Warning: ONNX model verification failed (max diff: {max_diff:.2e})")
    
    def benchmark_onnx_performance(
        self,
        onnx_path: str,
        input_size: Tuple[int, int] = (1920, 1080),
        num_iterations: int = 100
    ) -> Dict[str, float]:
        """
        Benchmark ONNX model performance

        Args:
            onnx_path: Path to ONNX model
            input_size: Input image size
            num_iterations: Number of iterations for benchmarking

        Returns:
            Performance metrics
        """
        if not ONNX_AVAILABLE:
            print("ONNX not available, skipping ONNX benchmarking")
            return {'fps': 0, 'mean_time_ms': 0}

        print(f"Benchmarking ONNX model performance...")

        # Create ONNX Runtime session
        ort_session = ort.InferenceSession(onnx_path)

        # Create dummy input
        dummy_input = np.random.randn(1, 3, input_size[1], input_size[0]).astype(np.float32)
        ort_inputs = {ort_session.get_inputs()[0].name: dummy_input}

        # Warmup
        for _ in range(10):
            _ = ort_session.run(None, ort_inputs)

        # Benchmark
        import time
        times = []

        for _ in range(num_iterations):
            start_time = time.time()
            _ = ort_session.run(None, ort_inputs)
            end_time = time.time()
            times.append((end_time - start_time) * 1000)  # Convert to ms

        # Calculate statistics
        times = np.array(times)

        performance_metrics = {
            'mean_time_ms': np.mean(times),
            'std_time_ms': np.std(times),
            'min_time_ms': np.min(times),
            'max_time_ms': np.max(times),
            'fps': 1000.0 / np.mean(times),
            'input_size': input_size,
            'num_iterations': num_iterations
        }

        print(f"ONNX Performance:")
        print(f"  Average time: {performance_metrics['mean_time_ms']:.2f} ms")
        print(f"  Average FPS: {performance_metrics['fps']:.2f}")

        return performance_metrics
    
    def prune_model(
        self,
        pruning_ratio: float = 0.2,
        structured: bool = False
    ) -> nn.Module:
        """
        Prune model to reduce parameters
        
        Args:
            pruning_ratio: Fraction of parameters to prune
            structured: Whether to use structured pruning
            
        Returns:
            Pruned model
        """
        print(f"Pruning model (ratio: {pruning_ratio:.2f})...")
        
        import torch.nn.utils.prune as prune
        
        # Get modules to prune
        modules_to_prune = []
        for name, module in self.model.named_modules():
            if isinstance(module, (nn.Conv2d, nn.Linear)):
                modules_to_prune.append((module, 'weight'))
        
        if structured:
            # Structured pruning (prune entire channels/filters)
            for module, param_name in modules_to_prune:
                if isinstance(module, nn.Conv2d):
                    prune.ln_structured(module, name=param_name, amount=pruning_ratio, n=2, dim=0)
                else:
                    prune.l1_unstructured(module, name=param_name, amount=pruning_ratio)
        else:
            # Unstructured pruning
            prune.global_unstructured(
                modules_to_prune,
                pruning_method=prune.L1Unstructured,
                amount=pruning_ratio,
            )
        
        # Calculate sparsity
        total_params = 0
        zero_params = 0
        
        for module, param_name in modules_to_prune:
            param = getattr(module, param_name)
            total_params += param.numel()
            zero_params += (param == 0).sum().item()
        
        sparsity = zero_params / total_params
        print(f"Model sparsity: {sparsity:.2%}")
        
        return self.model
    
    def _get_model_size(self, model: nn.Module) -> float:
        """Get model size in MB"""
        param_size = 0
        buffer_size = 0
        
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        size_mb = (param_size + buffer_size) / 1024 / 1024
        return size_mb

def optimize_for_deployment(
    model: nn.Module,
    calibration_data: torch.utils.data.DataLoader,
    output_dir: str,
    model_name: str = "optimized_model",
    quantize: bool = True,
    convert_onnx: bool = True,
    prune: bool = False,
    pruning_ratio: float = 0.2
) -> Dict[str, str]:
    """
    Complete optimization pipeline for deployment
    
    Args:
        model: Model to optimize
        calibration_data: Data for calibration
        output_dir: Directory to save optimized models
        model_name: Base name for saved models
        quantize: Whether to apply quantization
        convert_onnx: Whether to convert to ONNX
        prune: Whether to apply pruning
        pruning_ratio: Pruning ratio if pruning is enabled
        
    Returns:
        Dictionary of paths to optimized models
    """
    os.makedirs(output_dir, exist_ok=True)
    
    optimizer = ModelOptimizer(model)
    optimized_models = {}
    
    # Original model
    original_path = os.path.join(output_dir, f"{model_name}_original.pth")
    torch.save(model.state_dict(), original_path)
    optimized_models['original'] = original_path
    
    current_model = model
    
    # Pruning
    if prune:
        print("Applying pruning...")
        current_model = optimizer.prune_model(pruning_ratio)
        pruned_path = os.path.join(output_dir, f"{model_name}_pruned.pth")
        torch.save(current_model.state_dict(), pruned_path)
        optimized_models['pruned'] = pruned_path
    
    # Quantization
    if quantize:
        print("Applying quantization...")
        quantized_model = optimizer.quantize_model(calibration_data, 'dynamic')
        quantized_path = os.path.join(output_dir, f"{model_name}_quantized.pth")
        torch.save(quantized_model.state_dict(), quantized_path)
        optimized_models['quantized'] = quantized_path
        current_model = quantized_model
    
    # ONNX conversion
    if convert_onnx:
        print("Converting to ONNX...")
        onnx_path = os.path.join(output_dir, f"{model_name}.onnx")
        optimizer.model = current_model
        onnx_path = optimizer.convert_to_onnx(onnx_path)
        optimized_models['onnx'] = onnx_path
        
        # Benchmark ONNX performance
        perf_metrics = optimizer.benchmark_onnx_performance(onnx_path)
        
        # Save performance metrics
        import json
        perf_path = os.path.join(output_dir, f"{model_name}_performance.json")
        with open(perf_path, 'w') as f:
            json.dump(perf_metrics, f, indent=2)
    
    print(f"Optimization complete! Models saved to: {output_dir}")
    return optimized_models
